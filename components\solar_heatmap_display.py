import streamlit as st
from typing import Dict

def render_solar_heatmap_layers(analysis_data: Dict):
    """Display Google Solar API heatmap layers like the Google Solar demo"""
    
    solar_datalayers = analysis_data.get('solar_datalayers', {})
    satellite_data = analysis_data.get('satellite_imagery', {})
    
    if solar_datalayers.get('status') == 'success':
        st.subheader("🗺️ Google Solar API Heatmap Layers")
        st.markdown("*Professional solar analysis data from Google's satellite imagery*")
        
        imagery = solar_datalayers.get('imagery', {})
        
        # Main layout with satellite comparison
        comparison_cols = st.columns([1, 1])
        
        with comparison_cols[0]:
            st.markdown("**🛰️ Satellite View**")
            satellite_path = satellite_data.get('image_path')
            if satellite_path and len(satellite_path) > 5 and satellite_path != 'h':  # Validate path
                try:
                    st.image(satellite_path, 
                            caption="High-resolution satellite imagery",
                            use_container_width=True)
                except Exception as e:
                    st.warning(f"Unable to load satellite image: {str(e)[:100]}")
            else:
                st.info("Satellite imagery not available")
        
        with comparison_cols[1]:
            st.markdown("**☀️ Annual Solar Flux**")
            annual_flux_url = imagery.get('annualFluxUrl')
            if annual_flux_url and len(annual_flux_url) > 10:  # Valid URL check
                try:
                    st.image(annual_flux_url, 
                            caption="Annual sun exposure (kWh/m²/year)",
                            use_container_width=True)
                except Exception as e:
                    st.warning(f"Unable to load annual flux layer: {str(e)[:100]}")
            else:
                st.warning("❗ No annual flux layer available for this location")
        
        # Secondary heatmap layers
        st.markdown("---")
        heatmap_cols = st.columns([1, 1])
        
        with heatmap_cols[0]:
            st.markdown("**🏠 Roof Mask Layer**")
            mask_url = imagery.get('maskUrl')
            if mask_url and len(mask_url) > 10:  # Valid URL check
                try:
                    st.image(mask_url, 
                            caption="Building and roof detection mask",
                            use_container_width=True)
                except Exception as e:
                    st.warning(f"Unable to load roof mask layer: {str(e)[:100]}")
            else:
                st.warning("❗ No roof mask layer available for this location")
        
        with heatmap_cols[1]:
            st.markdown("**🌐 DSM (Digital Surface Model)**")
            dsm_url = imagery.get('dsmUrl')
            if dsm_url and len(dsm_url) > 10:  # Valid URL check
                try:
                    st.image(dsm_url, 
                            caption="3D elevation and surface model",
                            use_container_width=True)
                except Exception as e:
                    st.warning(f"Unable to load DSM layer: {str(e)[:100]}")
            else:
                st.warning("❗ No DSM layer available for this location")
        
        # Additional layers if available
        monthly_flux_urls = imagery.get('monthlyFluxUrls', [])
        hourly_shade_urls = imagery.get('hourlyShadeUrls', [])
        
        if monthly_flux_urls or hourly_shade_urls:
            st.markdown("---")
            st.subheader("📊 Advanced Solar Data Layers")
            
            if monthly_flux_urls:
                with st.expander("📅 Monthly Solar Flux Layers"):
                    st.info(f"Available monthly layers: {len(monthly_flux_urls)} months")
                    month_cols = st.columns(min(3, len(monthly_flux_urls)))
                    
                    for i, url in enumerate(monthly_flux_urls[:3]):  # Show first 3 months
                        with month_cols[i % 3]:
                            if url and len(url) > 10:
                                try:
                                    st.image(url, 
                                           caption=f"Month {i+1} solar flux",
                                           use_container_width=True)
                                except Exception as e:
                                    st.warning(f"Unable to load month {i+1} data")
                            else:
                                st.info(f"Month {i+1} data unavailable")
            
            if hourly_shade_urls:
                with st.expander("🕐 Hourly Shade Analysis"):
                    st.info(f"Available hourly shade layers: {len(hourly_shade_urls)} time periods")
                    # Show sample hours
                    hour_cols = st.columns(min(3, len(hourly_shade_urls)))
                    
                    for i, url in enumerate(hourly_shade_urls[:3]):  # Show first 3 hours
                        with hour_cols[i % 3]:
                            if url and len(url) > 10:
                                try:
                                    st.image(url, 
                                           caption=f"Hour {i+1} shade pattern",
                                           use_container_width=True)
                                except Exception as e:
                                    st.warning(f"Unable to load hour {i+1} data")
                            else:
                                st.info(f"Hour {i+1} data unavailable")
        
        # Technical details
        with st.expander("🔍 Technical Layer Information"):
            st.json({
                "Annual Flux Available": bool(imagery.get('annualFluxUrl')),
                "Roof Mask Available": bool(imagery.get('maskUrl')),
                "DSM Available": bool(imagery.get('dsmUrl')),
                "Monthly Layers Count": len(monthly_flux_urls),
                "Hourly Layers Count": len(hourly_shade_urls),
                "API Status": solar_datalayers.get('status'),
                "Data Source": "Google Solar API DataLayers"
            })
    
    elif solar_datalayers.get('status') == 'error':
        st.warning("❗ Solar heatmap layers unavailable for this location")
        st.info("This may be because:")
        st.markdown("""
        - The location is outside Google Solar API coverage area
        - The area doesn't have sufficient satellite data
        - The API quota has been exceeded
        """)
        
        error_msg = solar_datalayers.get('message', 'Unknown error')
        with st.expander("📋 Error Details"):
            st.text(error_msg)
        
        # Show available satellite data instead
        satellite_data = analysis_data.get('satellite_imagery', {})
        if satellite_data.get('status') == 'success' and satellite_data.get('image_path'):
            st.subheader("🛰️ Available Satellite Imagery")
            satellite_path = satellite_data.get('image_path')
            if satellite_path and len(satellite_path) > 5:
                try:
                    st.image(satellite_path, 
                            caption="High-resolution satellite view",
                            use_container_width=True)
                except Exception as e:
                    st.error("Unable to display satellite imagery")
    
    else:
        st.info("🔄 Solar heatmap data will be displayed here after analysis")

def render_heatmap_comparison_grid(analysis_data: Dict):
    """Render a comprehensive comparison grid of all available layers"""
    
    solar_datalayers = analysis_data.get('solar_datalayers', {})
    satellite_data = analysis_data.get('satellite_imagery', {})
    
    if solar_datalayers.get('status') == 'success':
        imagery = solar_datalayers.get('imagery', {})
        
        st.subheader("🔥 Complete Solar Heatmap Analysis Grid")
        
        # Create a 2x2 grid for comprehensive comparison
        grid_cols = st.columns(2)
        
        # Row 1
        with grid_cols[0]:
            st.markdown("**🛰️ Original Satellite**")
            if satellite_data.get('image_path'):
                try:
                    st.image(satellite_data['image_path'], use_container_width=True)
                except Exception as e:
                    st.warning("Satellite image unavailable")
            else:
                st.info("No satellite image available")
        
        with grid_cols[1]:
            st.markdown("**☀️ Annual Solar Flux**")
            annual_flux_url = imagery.get('annualFluxUrl')
            if annual_flux_url and len(annual_flux_url) > 10:
                try:
                    st.image(annual_flux_url, use_container_width=True)
                except Exception as e:
                    st.warning("Solar flux layer unavailable")
            else:
                st.info("No solar flux data available")
        
        # Row 2
        grid_cols2 = st.columns(2)
        
        with grid_cols2[0]:
            st.markdown("**🏠 Building Mask**")
            mask_url = imagery.get('maskUrl')
            if mask_url and len(mask_url) > 10:
                try:
                    st.image(mask_url, use_container_width=True)
                except Exception as e:
                    st.warning("Building mask layer unavailable")
            else:
                st.info("No building mask data available")
        
        with grid_cols2[1]:
            st.markdown("**🌐 Surface Model (DSM)**")
            dsm_url = imagery.get('dsmUrl')
            if dsm_url and len(dsm_url) > 10:
                try:
                    st.image(dsm_url, use_container_width=True)
                except Exception as e:
                    st.warning("DSM layer unavailable")
            else:
                st.info("No DSM data available")
        
        # Layer interpretation guide
        with st.expander("📖 Layer Interpretation Guide"):
            st.markdown("""
            **Solar Flux Layer**: Shows annual solar energy potential
            - Red/Yellow: High solar exposure (>1500 kWh/m²/year)
            - Green/Blue: Moderate solar exposure (800-1500 kWh/m²/year)
            - Dark/Purple: Low solar exposure (<800 kWh/m²/year)
            
            **Building Mask**: Identifies roof and building surfaces
            - White areas: Detected building/roof surfaces
            - Dark areas: Non-building areas (ground, vegetation, etc.)
            
            **DSM (Digital Surface Model)**: Shows 3D elevation data
            - Lighter colors: Higher elevation
            - Darker colors: Lower elevation
            - Helps identify roof orientation and shading factors
            """)