import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from PIL import Image
import numpy as np
from utils.image_processor import ImageProcessor
from .solar_heatmap_display import render_solar_heatmap_layers, render_heatmap_comparison_grid
from typing import Dict

def render_solar_visualization(analysis_data: Dict):
    """Render comprehensive solar visualization components like Project Sunroof"""
    
    # Primary satellite view first - like Project Sunroof
    render_satellite_visualization(analysis_data)
    
    # Google Solar API Heatmap Layers
    st.markdown("---")
    render_solar_heatmap_layers(analysis_data)
    
    # Additional analysis - only show when satellite analysis is available
    satellite_data = analysis_data.get('satellite_imagery', {})
    if satellite_data.get('status') == 'success':
        st.markdown("---")
        st.subheader("📊 Multi-Angle Street Views")
        render_street_view_visualization(analysis_data)
        
        # Advanced heatmap comparison grid
        st.markdown("---")
        render_heatmap_comparison_grid(analysis_data)

def render_satellite_visualization(analysis_data: Dict):
    """Render satellite imagery with solar overlay like Project Sunroof"""
    
    satellite_data = analysis_data.get('satellite_imagery', {})
    
    if satellite_data.get('status') == 'success' and satellite_data.get('image_path'):
        # Large prominent image display like Project Sunroof
        st.markdown("### Your Property's Solar Potential")
        
        # Process image with enhanced solar overlay
        image_processor = ImageProcessor()
        
        try:
            # Create comprehensive solar visualization with enhanced mapping
            composite_path = image_processor.create_composite_solar_visualization(
                satellite_data['image_path'],
                analysis_data
            )

            # Display the analyzed image prominently
            composite_image = Image.open(composite_path)
            st.image(composite_image, caption="Enhanced Solar Potential Analysis", use_container_width=True)
            
            # Image controls and options like Project Sunroof
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col1:
                # Create and display legend
                legend_path = image_processor.create_legend()
                if legend_path:
                    st.markdown("**Legend**")
                    legend_image = Image.open(legend_path)
                    st.image(legend_image, width=250)
            
            with col2:
                # Image details and controls
                st.markdown("**Image Details**")
                st.write(f"📍 **Location:** {satellite_data.get('coordinates', {}).get('lat', 0):.6f}, {satellite_data.get('coordinates', {}).get('lng', 0):.6f}")
                st.write(f"🔍 **Zoom Level:** {satellite_data.get('zoom_level', 'N/A')}")
                st.write(f"📏 **Resolution:** {satellite_data.get('size', '800x800')} pixels")
                
                # Toggle views
                if st.checkbox("Show Original Satellite Image", key="show_original"):
                    original_image = Image.open(satellite_data['image_path'])
                    st.image(original_image, caption="Original Satellite View", use_container_width=True)
                
                # Show hybrid view if available
                if satellite_data.get('hybrid_path'):
                    if st.checkbox("Show Hybrid View (Labels)", key="show_hybrid"):
                        hybrid_image = Image.open(satellite_data['hybrid_path'])
                        st.image(hybrid_image, caption="Hybrid View with Labels", use_container_width=True)
            
            with col3:
                # Quick stats
                roof_analysis = analysis_data.get('roof_analysis', {})
                if roof_analysis:
                    st.markdown("**Quick Stats**")
                    suitability_score = roof_analysis.get('solar_suitability_score', 0)
                    if suitability_score >= 8:
                        st.success(f"🟢 Excellent ({suitability_score}/10)")
                    elif suitability_score >= 6:
                        st.warning(f"🟡 Good ({suitability_score}/10)")
                    else:
                        st.error(f"🔴 Limited ({suitability_score}/10)")
                    
                    st.write(f"📐 **Usable Area:** {roof_analysis.get('usable_area_sqft', 0):,.0f} sq ft")
                    st.write(f"🏠 **Condition:** {roof_analysis.get('roof_condition', 'N/A')}")
                    st.write(f"🧭 **Orientation:** {roof_analysis.get('roof_orientation', 'N/A')}")
            
            # Roof analysis summary
            roof_analysis = analysis_data.get('roof_analysis', {})
            if roof_analysis:
                st.write("**🔍 Analysis Summary**")
                
                metrics_cols = st.columns(4)
                with metrics_cols[0]:
                    st.metric(
                        "Usable Area", 
                        f"{roof_analysis.get('usable_area_sqft', 0):,.0f} sq ft"
                    )
                with metrics_cols[1]:
                    st.metric(
                        "Suitability Score", 
                        f"{roof_analysis.get('solar_suitability_score', 0)}/10"
                    )
                with metrics_cols[2]:
                    st.metric(
                        "Roof Condition", 
                        roof_analysis.get('roof_condition', 'N/A')
                    )
                with metrics_cols[3]:
                    st.metric(
                        "Orientation", 
                        roof_analysis.get('roof_orientation', 'N/A')
                    )
                
                # Detailed assessment
                with st.expander("📋 Detailed Assessment"):
                    st.write(f"**Shading Assessment:** {roof_analysis.get('shading_assessment', 'N/A')}")
                    st.write(f"**Structural Assessment:** {roof_analysis.get('structural_assessment', 'N/A')}")
                    
                    if roof_analysis.get('panel_placement_zones'):
                        st.write("**Recommended Panel Zones:**")
                        for zone in roof_analysis['panel_placement_zones']:
                            st.write(f"• {zone}")
        
        except Exception as e:
            st.error(f"Error processing satellite imagery: {e}")
            # Show original image as fallback
            if satellite_data.get('image_path'):
                original_image = Image.open(satellite_data['image_path'])
                st.image(original_image, caption="Satellite Image", use_container_width=True)
    
    else:
        st.warning("⚠️ Satellite imagery not available")

def render_street_view_visualization(analysis_data: Dict):
    """Render Street View imagery for multi-angle roof analysis"""
    
    st.subheader("🏠 Street View Analysis")
    
    street_view_data = analysis_data.get('street_view', {})
    
    if street_view_data.get('status') == 'success' and street_view_data.get('images'):
        st.write("**Multi-angle roof visualization for comprehensive assessment**")
        
        # Display Street View images in a grid
        images = street_view_data['images']
        
        if len(images) >= 4:
            # 2x2 grid for 4 directions
            row1_cols = st.columns(2)
            row2_cols = st.columns(2)
            
            cols = [row1_cols[0], row1_cols[1], row2_cols[0], row2_cols[1]]
            
            for i, img_data in enumerate(images[:4]):
                with cols[i]:
                    try:
                        street_image = Image.open(img_data['image_path'])
                        st.image(
                            street_image, 
                            caption=f"{img_data['direction']} View ({img_data['heading']}°)",
                            use_container_width=True
                        )
                    except Exception as e:
                        st.error(f"Error loading {img_data['direction']} view: {e}")
        
        else:
            # Single row for fewer images
            cols = st.columns(len(images))
            for i, img_data in enumerate(images):
                with cols[i]:
                    try:
                        street_image = Image.open(img_data['image_path'])
                        st.image(
                            street_image, 
                            caption=f"{img_data['direction']} View",
                            use_container_width=True
                        )
                    except Exception as e:
                        st.error(f"Error loading street view: {e}")
        
        # Street View analysis insights
        st.write("**🔍 Street View Insights**")
        st.info("""
        Street View imagery helps identify:
        • Roof accessibility and structural details
        • Nearby obstructions (trees, buildings)
        • Roof angle and orientation
        • Potential shading sources
        • Overall property condition
        """)
    
    else:
        st.warning("⚠️ Street View imagery not available for this location")

def render_solar_metrics_visualization(analysis_data: Dict):
    """Render solar metrics and technical data visualizations"""
    
    st.subheader("☀️ Solar Resource Metrics")
    
    solar_data = analysis_data.get('solar_data', {})
    system_estimates = analysis_data.get('system_estimates', {})
    elevation_data = analysis_data.get('elevation', {})
    
    # Key solar metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Annual Sunlight",
            f"{system_estimates.get('annual_sunlight_hours', 0):,.0f} hrs",
            help="Total hours of usable sunlight per year"
        )
    
    with col2:
        st.metric(
            "Solar Irradiance",
            f"{solar_data.get('annual_irradiance_kwh_per_m2', 0):,.0f} kWh/m²",
            help="Annual solar energy potential per square meter"
        )
    
    with col3:
        st.metric(
            "Elevation",
            f"{elevation_data.get('elevation_feet', 0):,.0f} ft",
            help="Property elevation above sea level"
        )
    
    with col4:
        st.metric(
            "System Capacity",
            f"{system_estimates.get('system_capacity_kw', 0):.1f} kW",
            help="Recommended solar system size"
        )
    
    # Solar potential comparison chart
    st.write("**📊 Solar Potential Comparison**")
    
    # Create comparison data
    metrics = {
        'Metric': ['Sunlight Hours', 'System Capacity', 'Annual Generation', 'Roof Suitability'],
        'Your Property': [
            system_estimates.get('annual_sunlight_hours', 0) / 20,  # Scale for visualization
            system_estimates.get('system_capacity_kw', 0) * 10,
            system_estimates.get('annual_generation_kwh', 0) / 100,
            analysis_data.get('roof_analysis', {}).get('solar_suitability_score', 0) * 10
        ],
        'National Average': [75, 60, 120, 70]  # Scaled reference values
    }
    
    # Create radar chart
    fig = create_solar_radar_chart(metrics)
    st.plotly_chart(fig, use_container_width=True, key="solar_radar_chart")
    
    # Detailed technical data
    with st.expander("🔧 Technical Data"):
        tech_data = {
            'Parameter': [
                'Maximum Panels',
                'Panel Efficiency',
                'System Losses',
                'Capacity Factor',
                'Annual Production',
                'CO₂ Offset (Annual)'
            ],
            'Value': [
                f"{system_estimates.get('max_panels', 0)} panels",
                f"{analysis_data.get('parameters', {}).get('panel_efficiency', 20)}%",
                f"{analysis_data.get('parameters', {}).get('system_losses', 14)}%",
                "15% (typical)",
                f"{system_estimates.get('annual_generation_kwh', 0):,.0f} kWh",
                f"{system_estimates.get('annual_generation_kwh', 0) * 0.0004:,.1f} tons"
            ]
        }
        
        st.table(tech_data)

def render_energy_production_visualization(analysis_data: Dict):
    """Render energy production and financial projections"""
    
    st.subheader("⚡ Energy Production Forecast")
    
    system_estimates = analysis_data.get('system_estimates', {})
    
    # Monthly production estimate (simplified seasonal variation)
    monthly_production = generate_monthly_production_data(
        system_estimates.get('annual_generation_kwh', 0)
    )
    
    # Monthly production chart
    fig_monthly = px.bar(
        x=list(monthly_production.keys()),
        y=list(monthly_production.values()),
        title="Estimated Monthly Energy Production",
        labels={'x': 'Month', 'y': 'Energy (kWh)'},
        color=list(monthly_production.values()),
        color_continuous_scale='viridis'
    )
    fig_monthly.update_layout(showlegend=False)
    st.plotly_chart(fig_monthly, use_container_width=True, key="monthly_production_chart")
    
    # Annual savings projection
    st.write("**💰 20-Year Financial Projection**")
    
    years = list(range(1, 21))
    annual_savings = system_estimates.get('annual_savings', 0)
    electricity_rate = analysis_data.get('parameters', {}).get('electricity_rate', 0.12)
    
    # Calculate cumulative savings with rate escalation
    cumulative_savings = []
    cumulative = 0
    
    for year in years:
        # Assume 2.5% annual rate increase and 0.5% system degradation
        year_savings = annual_savings * (1.025 ** (year - 1)) * (0.995 ** (year - 1))
        cumulative += year_savings
        cumulative_savings.append(cumulative)
    
    # Subtract initial investment
    net_investment = system_estimates.get('net_system_cost', 0)
    net_cumulative = [savings - net_investment for savings in cumulative_savings]
    
    # Create savings projection chart
    fig_savings = go.Figure()
    
    fig_savings.add_trace(go.Scatter(
        x=years,
        y=net_cumulative,
        mode='lines+markers',
        name='Net Cumulative Savings',
        line=dict(color='green', width=3),
        fill='tozeroy',
        fillcolor='rgba(0,255,0,0.1)'
    ))
    
    # Add break-even line
    fig_savings.add_hline(
        y=0,
        line_dash="dash",
        line_color="red",
        annotation_text="Break-even"
    )
    
    fig_savings.update_layout(
        title="20-Year Net Savings Projection",
        xaxis_title="Year",
        yaxis_title="Cumulative Net Savings ($)",
        hovermode='x unified'
    )
    
    st.plotly_chart(fig_savings, use_container_width=True, key="savings_projection_chart")
    
    # Key financial metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Payback Period",
            f"{system_estimates.get('payback_period', 0):.1f} years",
            help="Time to recover initial investment"
        )
    
    with col2:
        st.metric(
            "20-Year Savings",
            f"${system_estimates.get('savings_20_years', 0):,.0f}",
            help="Total net savings over 20 years"
        )
    
    with col3:
        roi = (system_estimates.get('savings_20_years', 0) / max(system_estimates.get('net_system_cost', 1), 1)) * 100
        st.metric(
            "ROI (20 years)",
            f"{roi:.0f}%",
            help="Return on investment over 20 years"
        )

def create_solar_radar_chart(metrics: dict) -> go.Figure:
    """Create radar chart for solar metrics comparison"""
    
    fig = go.Figure()
    
    # Add property data
    fig.add_trace(go.Scatterpolar(
        r=metrics['Your Property'],
        theta=metrics['Metric'],
        fill='toself',
        name='Your Property',
        line_color='blue'
    ))
    
    # Add national average
    fig.add_trace(go.Scatterpolar(
        r=metrics['National Average'],
        theta=metrics['Metric'],
        fill='toself',
        name='National Average',
        line_color='red',
        opacity=0.6
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 150]
            )),
        showlegend=True,
        title="Solar Potential Comparison"
    )
    
    return fig

def generate_monthly_production_data(annual_kwh: float) -> dict:
    """Generate monthly production estimates with seasonal variation"""
    
    # Seasonal factors (simplified for demonstration)
    seasonal_factors = {
        'Jan': 0.65, 'Feb': 0.75, 'Mar': 0.85, 'Apr': 0.95,
        'May': 1.10, 'Jun': 1.20, 'Jul': 1.25, 'Aug': 1.15,
        'Sep': 1.00, 'Oct': 0.85, 'Nov': 0.65, 'Dec': 0.60
    }
    
    # Calculate base monthly production
    base_monthly = annual_kwh / 12
    
    # Apply seasonal factors
    monthly_production = {
        month: base_monthly * factor 
        for month, factor in seasonal_factors.items()
    }
    
    return monthly_production
