modules = ["python-3.11"]

[nix]
channel = "stable-24_05"
packages = ["freetype", "glibcLocales", "libGL", "libGLU", "libxcrypt"]

[deployment]
deploymentTarget = "autoscale"
run = ["streamlit", "run", "app.py", "--server.port", "5000"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Solar Analyzer Server"

[[workflows.workflow]]
name = "Solar Analyzer Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "streamlit run app.py --server.port 5000"
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80
