✅ 🔧 Prompt Instruction (For You or Your Dev/LLM)
Prompt:

"Add support for displaying the solar heatmap layer (annual sun flux, roof mask, DSM) from the Google Solar API to the current Streamlit app. For a given lat/lon or address, fetch the data from the dataLayers:get endpoint of the Solar API and display the resulting heatmap images side-by-side with the satellite image (or overlay if possible). Ensure to show at least the annualFluxUrl, maskUrl, and dsmUrl layers visually, just like the Google Solar demo."

Use API: https://solar.googleapis.com/v1/dataLayers:get with parameters: location.latitude, location.longitude, radiusMeters=50, and view=FULL_LAYERS. Use st.image() for each resulting tile URL. If the tile layers are unavailable, show a warning."

✅ 🔁 Code Snippet (Modular)
1. 📦 Add this function in your code:
python
Copy
Edit
def get_solar_datalayers(lat, lon, api_key):
    """Fetch solar heatmap layers (flux, mask, dsm) from Google Solar API"""
    url = (
        f"https://solar.googleapis.com/v1/dataLayers:get"
        f"?location.latitude={lat}&location.longitude={lon}"
        f"&radiusMeters=50&view=FULL_LAYERS&key={api_key}"
    )
    response = requests.get(url)
    return response.json()
2. 🧠 Inside your Streamlit logic (after you have lat, lon):
python
Copy
Edit
datalayers = get_solar_datalayers(lat, lon, API_KEY)

if "imagery" in datalayers:
    imagery = datalayers["imagery"]
    st.subheader("🟡 Solar API Heatmap Layers")

    col1, col2 = st.columns(2)

    if imagery.get("annualFluxUrl"):
        with col1:
            st.image(imagery["annualFluxUrl"], caption="☀️ Annual Sun Exposure (Flux)")
    else:
        st.warning("No annual flux layer found")

    if imagery.get("maskUrl"):
        with col2:
            st.image(imagery["maskUrl"], caption="🏠 Roof Mask")
    else:
        st.warning("No roof mask layer found")

    st.image(imagery.get("dsmUrl"), caption="🌐 DSM (Elevation Model)")
else:
    st.warning("❗ No imagery data available for this location.")
3. (Optional) 📍 Show Satellite View for Comparison
python
Copy
Edit
static_map_url = (
    f"https://maps.googleapis.com/maps/api/staticmap"
    f"?center={lat},{lon}&zoom=20&size=600x400&maptype=satellite&key={API_KEY}"
)
st.subheader("🛰️ Satellite View")
st.image(static_map_url, caption="Static Satellite Map")
✅ Output Preview (Final Layout)
Satellite View	Heatmap	Mask	DSM
st.image()	annualFluxUrl	maskUrl	dsmUrl