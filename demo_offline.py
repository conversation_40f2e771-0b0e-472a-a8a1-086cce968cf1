#!/usr/bin/env python3
"""
Offline demo of enhanced solar panel mapping functionality
This demo shows the enhanced image processing capabilities without requiring API keys
"""

import os
import sys
from PIL import Image, ImageDraw
from utils.image_processor import ImageProcessor

def create_demo_satellite_image():
    """Create a demo satellite image for testing"""
    print("🎨 Creating demo satellite image...")
    
    # Create a realistic-looking satellite image
    width, height = 800, 800
    image = Image.new('RGB', (width, height), color=(34, 139, 34))  # Forest green base
    draw = ImageDraw.Draw(image)
    
    # Add some building-like structures
    # Main building (house)
    house_color = (169, 169, 169)  # Gray roof
    draw.rectangle([300, 250, 500, 400], fill=house_color)
    draw.rectangle([320, 270, 480, 380], fill=(139, 69, 19))  # Brown house body
    
    # Garage
    draw.rectangle([500, 320, 600, 400], fill=house_color)
    
    # Driveway
    draw.rectangle([250, 400, 600, 450], fill=(105, 105, 105))
    
    # Some trees/vegetation
    tree_color = (0, 100, 0)
    for i in range(10):
        x = 100 + i * 60
        y = 500 + (i % 3) * 50
        draw.ellipse([x, y, x+40, y+40], fill=tree_color)
    
    # Save demo image
    demo_path = "demo_satellite.png"
    image.save(demo_path)
    print(f"   ✅ Demo satellite image saved: {demo_path}")
    
    return demo_path

def create_mock_analysis_data():
    """Create mock analysis data similar to Google Solar API response"""
    print("📊 Creating mock solar analysis data...")
    
    mock_data = {
        'satellite_imagery': {
            'status': 'success',
            'image_path': 'demo_satellite.png',
            'coordinates': {'lat': 37.4419, 'lng': -122.1430}
        },
        'solar_data': {
            'status': 'success',
            'coordinates': {'lat': 37.4419, 'lng': -122.1430},
            'solar_potential': {
                'solarPanels': [
                    {
                        'center': {'latitude': 37.4419, 'longitude': -122.1430},
                        'yearlyEnergyDcKwh': 2800,
                        'orientation': 'LANDSCAPE',
                        'segmentIndex': 0
                    },
                    {
                        'center': {'latitude': 37.4420, 'longitude': -122.1431},
                        'yearlyEnergyDcKwh': 2650,
                        'orientation': 'LANDSCAPE',
                        'segmentIndex': 0
                    },
                    {
                        'center': {'latitude': 37.4418, 'longitude': -122.1429},
                        'yearlyEnergyDcKwh': 2400,
                        'orientation': 'PORTRAIT',
                        'segmentIndex': 0
                    },
                    {
                        'center': {'latitude': 37.4421, 'longitude': -122.1432},
                        'yearlyEnergyDcKwh': 2200,
                        'orientation': 'LANDSCAPE',
                        'segmentIndex': 1
                    },
                    {
                        'center': {'latitude': 37.4417, 'longitude': -122.1428},
                        'yearlyEnergyDcKwh': 1900,
                        'orientation': 'PORTRAIT',
                        'segmentIndex': 1
                    },
                    {
                        'center': {'latitude': 37.4422, 'longitude': -122.1433},
                        'yearlyEnergyDcKwh': 1600,
                        'orientation': 'LANDSCAPE',
                        'segmentIndex': 1
                    }
                ],
                'panelWidthMeters': 1.65,
                'panelHeightMeters': 0.99,
                'roofSegmentStats': [
                    {'azimuthDegrees': 180},  # South-facing (optimal)
                    {'azimuthDegrees': 90}    # East-facing
                ]
            }
        },
        'solar_datalayers': {
            'status': 'success',
            'imagery': {
                'annualFluxUrl': None,  # No real URL for demo
                'maskUrl': None,
                'dsmUrl': None
            }
        },
        'coordinates': {'lat': 37.4419, 'lng': -122.1430}
    }
    
    print(f"   ✅ Mock data created with {len(mock_data['solar_data']['solar_potential']['solarPanels'])} solar panels")
    return mock_data

def demo_enhanced_solar_mapping():
    """Demonstrate the enhanced solar panel mapping functionality"""
    print("\n🚀 Enhanced Solar Panel Mapping Demo")
    print("=" * 60)
    
    # Create demo data
    satellite_path = create_demo_satellite_image()
    analysis_data = create_mock_analysis_data()
    
    # Initialize image processor
    print("\n🔧 Initializing enhanced image processor...")
    image_processor = ImageProcessor()
    
    # Test 1: Enhanced solar panel overlay
    print("\n1️⃣ Testing Enhanced Solar Panel Overlay")
    try:
        overlay_path = image_processor.create_solar_overlay(satellite_path, analysis_data)
        print(f"   ✅ Enhanced overlay created: {overlay_path}")
        
        # Show panel details
        panels = analysis_data['solar_data']['solar_potential']['solarPanels']
        energies = [panel['yearlyEnergyDcKwh'] for panel in panels]
        print(f"   📊 Panel energy range: {min(energies)} - {max(energies)} kWh/year")
        
    except Exception as e:
        print(f"   ❌ Error creating overlay: {e}")
    
    # Test 2: Color palette demonstration
    print("\n2️⃣ Testing Energy-Based Color Palette")
    try:
        palette = image_processor._create_energy_palette()
        print("   🎨 Energy color palette:")
        
        energy_descriptions = [
            "High energy (2800+ kWh) - Bright green",
            "Good energy (2400+ kWh) - Green", 
            "Medium energy (2000+ kWh) - Yellow-green",
            "Low energy (1600+ kWh) - Gray-green",
            "Very low energy (1200+ kWh) - Purple",
            "Poor energy (800+ kWh) - Magenta",
            "No energy (0-800 kWh) - Red"
        ]
        
        for i, (color, desc) in enumerate(zip(palette, energy_descriptions)):
            print(f"      Color {i}: RGB{tuple(color)} - {desc}")
            
    except Exception as e:
        print(f"   ❌ Error testing palette: {e}")
    
    # Test 3: Coordinate conversion
    print("\n3️⃣ Testing Coordinate Conversion")
    try:
        panels = analysis_data['solar_data']['solar_potential']['solarPanels']
        print("   🗺️ Panel positions (lat/lng → pixel coordinates):")
        
        for i, panel in enumerate(panels[:3]):  # Show first 3 panels
            lat = panel['center']['latitude']
            lng = panel['center']['longitude']
            energy = panel['yearlyEnergyDcKwh']
            orientation = panel['orientation']
            
            x, y = image_processor._latlon_to_pixel(lat, lng, 800, 800, analysis_data['solar_data'])
            print(f"      Panel {i+1}: ({lat:.6f}, {lng:.6f}) → ({x}, {y}) | {energy} kWh | {orientation}")
            
    except Exception as e:
        print(f"   ❌ Error testing coordinates: {e}")
    
    # Test 4: Composite visualization
    print("\n4️⃣ Testing Composite Visualization")
    try:
        composite_path = image_processor.create_composite_solar_visualization(satellite_path, analysis_data)
        print(f"   ✅ Composite visualization created: {composite_path}")
        
        # Check if file was created and get size
        if os.path.exists(composite_path):
            file_size = os.path.getsize(composite_path)
            print(f"   📁 File size: {file_size:,} bytes")
            
            # Try to get image dimensions
            with Image.open(composite_path) as img:
                print(f"   📐 Image dimensions: {img.size[0]}x{img.size[1]} pixels")
                print(f"   🎨 Image mode: {img.mode}")
        
    except Exception as e:
        print(f"   ❌ Error creating composite: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Enhanced Solar Panel Mapping Demo Complete!")
    print("\nGenerated Files:")
    print("• demo_satellite.png - Demo satellite image")
    print("• demo_satellite_solar_overlay.png - Enhanced solar overlay")
    print("• demo_satellite_composite.png - Composite visualization")
    print("\n🎯 Key Features Demonstrated:")
    print("• Energy-based color coding for solar panels")
    print("• Accurate coordinate conversion (lat/lng → pixels)")
    print("• Panel orientation and rotation handling")
    print("• Composite visualization pipeline")
    print("• Robust error handling and fallbacks")

def show_results():
    """Display information about generated files"""
    print("\n📁 Generated Demo Files:")
    
    files_to_check = [
        "demo_satellite.png",
        "demo_satellite_solar_overlay.png", 
        "demo_satellite_composite.png"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"   ✅ {filename} ({file_size:,} bytes)")
        else:
            print(f"   ❌ {filename} (not found)")
    
    print("\n💡 To view the results:")
    print("   Open the generated PNG files in any image viewer")
    print("   Compare the original satellite image with the enhanced overlays")

if __name__ == "__main__":
    print("🌞 Solar Potential Analyzer - Offline Demo")
    print("Enhanced Solar Panel Mapping Demonstration")
    print("=" * 60)
    print("This demo showcases the enhanced image processing capabilities")
    print("without requiring Google API keys or internet connection.")
    print("=" * 60)
    
    try:
        # Run the demo
        demo_enhanced_solar_mapping()
        
        # Show results
        show_results()
        
        print("\n🎉 Demo completed successfully!")
        print("The enhanced solar panel mapping system is ready for production use.")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        print("Please check that all dependencies are installed correctly.")
        sys.exit(1)
