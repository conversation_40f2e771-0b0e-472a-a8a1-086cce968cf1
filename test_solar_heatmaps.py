#!/usr/bin/env python3
"""
Test script for Google Solar API DataLayers (heatmap) integration
"""

from utils.google_apis import GoogleAPIs
import requests

def test_solar_datalayers():
    """Test Google Solar API DataLayers integration"""
    
    print("🔄 Testing Google Solar API DataLayers...")
    
    # Initialize API handler
    google_apis = GoogleAPIs()
    
    # Test coordinates (Palo Alto, CA - known to have Solar API coverage)
    test_locations = [
        {"name": "Palo Alto, CA", "lat": 37.4419, "lng": -122.1430},
        {"name": "San Francisco, CA", "lat": 37.7749, "lng": -122.4194},
        {"name": "New York, NY", "lat": 40.7128, "lng": -74.0060}
    ]
    
    for location in test_locations:
        print(f"\n📍 Testing location: {location['name']} ({location['lat']}, {location['lng']})")
        
        # Test Solar DataLayers API
        result = google_apis.get_solar_datalayers(location['lat'], location['lng'])
        
        print(f"   Status: {result.get('status')}")
        
        if result.get('status') == 'success':
            imagery = result.get('imagery', {})
            
            print(f"   ✅ Annual Flux URL: {'Available' if imagery.get('annualFluxUrl') else 'Not available'}")
            print(f"   ✅ Mask URL: {'Available' if imagery.get('maskUrl') else 'Not available'}")
            print(f"   ✅ DSM URL: {'Available' if imagery.get('dsmUrl') else 'Not available'}")
            print(f"   📊 Monthly flux layers: {len(imagery.get('monthlyFluxUrls', []))}")
            print(f"   🕐 Hourly shade layers: {len(imagery.get('hourlyShadeUrls', []))}")
            
            # Test if URLs are accessible
            for layer_name, url in [
                ("Annual Flux", imagery.get('annualFluxUrl')),
                ("Roof Mask", imagery.get('maskUrl')),
                ("DSM", imagery.get('dsmUrl'))
            ]:
                if url:
                    try:
                        response = requests.head(url, timeout=10)
                        if response.status_code == 200:
                            print(f"   🌐 {layer_name}: URL accessible")
                        else:
                            print(f"   ⚠️ {layer_name}: URL returned {response.status_code}")
                    except Exception as e:
                        print(f"   ❌ {layer_name}: URL test failed - {str(e)}")
            
            return True
            
        elif result.get('status') == 'error':
            print(f"   ❌ Error: {result.get('message')}")
            
    return False

def test_manual_api_call():
    """Test manual API call to verify endpoint"""
    
    print("\n🔧 Testing manual Solar DataLayers API call...")
    
    api_key = google_apis.api_key if 'google_apis' in globals() else None
    if not api_key:
        google_apis = GoogleAPIs()
        api_key = google_apis.api_key
    
    # Test with Palo Alto coordinates
    lat, lng = 37.4419, -122.1430
    
    url = f"https://solar.googleapis.com/v1/dataLayers:get"
    params = {
        'location.latitude': lat,
        'location.longitude': lng,
        'radiusMeters': 50,
        'view': 'FULL_LAYERS',
        'key': api_key
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"   HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Response received")
            print(f"   📋 Keys in response: {list(data.keys())}")
            
            if 'imagery' in data:
                imagery = data['imagery']
                print(f"   🗺️ Imagery data available with {len(imagery)} fields")
                
                for key in ['annualFluxUrl', 'maskUrl', 'dsmUrl']:
                    if key in imagery:
                        print(f"   - {key}: Available")
                        # Show partial URL for verification
                        url_preview = imagery[key][:50] + "..." if len(imagery[key]) > 50 else imagery[key]
                        print(f"     Preview: {url_preview}")
            
            return True
        else:
            print(f"   ❌ API call failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"   ❌ Manual API test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Solar API DataLayers Test Suite")
    print("=" * 50)
    
    # Test integrated function
    integration_success = test_solar_datalayers()
    
    # Test manual API call
    manual_success = test_manual_api_call()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Integration Test: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    print(f"   Manual API Test: {'✅ PASSED' if manual_success else '❌ FAILED'}")
    
    if integration_success or manual_success:
        print("\n🎉 Solar DataLayers integration is ready!")
        print("   Features available:")
        print("   - Annual solar flux heatmaps")
        print("   - Building roof mask detection")
        print("   - Digital Surface Model (DSM)")
        print("   - Monthly and hourly solar data")
    else:
        print("\n⚠️ Solar DataLayers may need API key verification")
        print("   Check Google Cloud Console for Solar API access")