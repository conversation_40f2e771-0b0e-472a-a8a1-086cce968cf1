#!/usr/bin/env python3
"""
Local runner for Solar Potential Analyzer with enhanced solar panel mapping
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'streamlit',
        'google-genai', 
        'requests',
        'pillow',
        'numpy',
        'plotly',
        'pydantic',
        'reportlab'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - Missing")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(missing_packages):
    """Install missing dependencies"""
    if not missing_packages:
        return True
    
    print(f"\n🔧 Installing {len(missing_packages)} missing packages...")
    
    try:
        # Try pip install
        cmd = [sys.executable, "-m", "pip", "install"] + missing_packages
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_api_keys():
    """Check if required API keys are configured"""
    print("\n🔑 Checking API keys...")
    
    google_key = os.getenv("GOOGLE_API_KEY")
    gemini_key = os.getenv("GEMINI_API_KEY")
    
    if not google_key:
        print("   ❌ GOOGLE_API_KEY not found in environment variables")
        print("      Get your key from: https://console.cloud.google.com/apis/credentials")
        print("      Enable: Maps Static API, Geocoding API, Solar API")
        return False
    else:
        print("   ✅ GOOGLE_API_KEY configured")
    
    if not gemini_key:
        print("   ❌ GEMINI_API_KEY not found in environment variables")
        print("      Get your key from: https://aistudio.google.com/app/apikey")
        return False
    else:
        print("   ✅ GEMINI_API_KEY configured")
    
    return True

def setup_environment():
    """Setup environment variables from .env file if it exists"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("📄 Loading environment variables from .env file...")
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("   ✅ Environment variables loaded")
        except Exception as e:
            print(f"   ⚠️ Error loading .env file: {e}")
    else:
        print("📄 No .env file found. Using system environment variables.")
        print("   💡 Create a .env file from .env.example for easier setup")

def run_streamlit():
    """Run the Streamlit application"""
    print("\n🚀 Starting Solar Potential Analyzer...")
    print("   📍 Enhanced solar panel mapping enabled")
    print("   🎨 GeoTiff-style visualization active")
    print("   🌐 Opening in browser...")
    
    try:
        # Run streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "app.py", "--server.port", "8501"]
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Solar Analyzer stopped by user")
    except Exception as e:
        print(f"\n❌ Error running application: {e}")

def main():
    """Main setup and run function"""
    print("🌞 Solar Potential Analyzer - Local Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check API keys
    if not check_api_keys():
        print("\n💡 Setup Instructions:")
        print("1. Copy .env.example to .env")
        print("2. Add your API keys to the .env file")
        print("3. Run this script again")
        sys.exit(1)
    
    # Check dependencies
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n🔧 Found {len(missing_packages)} missing dependencies")
        install_choice = input("Install missing packages? (y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_dependencies(missing_packages):
                print("\n❌ Failed to install dependencies. Please install manually:")
                print(f"   pip install {' '.join(missing_packages)}")
                sys.exit(1)
        else:
            print("\n❌ Cannot run without required dependencies")
            sys.exit(1)
    
    print("\n✅ All checks passed!")
    print("\n" + "=" * 60)
    
    # Run the application
    run_streamlit()

if __name__ == "__main__":
    main()
