import streamlit as st
from utils.google_apis import GoogleAPIs
from typing import Dict, Optional

def render_address_input() -> Optional[Dict]:
    """Render address input component like Project Sunroof"""
    
    # Initialize Google APIs
    try:
        google_apis = GoogleAPIs()
    except Exception as e:
        st.error(f"Google API initialization failed: {e}")
        return None
    
    # Large prominent search box like Project Sunroof
    st.markdown("### Find your home's solar potential")
    st.markdown("Search for your address to discover how much you could save with solar panels.")
    
    # Address input with better styling
    address_input = st.text_input(
        "Property Address",
        placeholder="Enter your address (e.g., 1600 Amphitheatre Parkway, Mountain View, CA)",
        help="Enter your complete street address for the most accurate analysis",
        label_visibility="collapsed"
    )
    
    if address_input and len(address_input) > 5:
        # Real-time search as user types (simplified)
        if len(address_input) > 10:
            with st.spinner("🔍 Finding your property..."):
                # Geocode the address
                geocode_result = google_apis.geocode_address(address_input)
                
                if geocode_result['status'] == 'success':
                    st.success("✅ Property located!")
                    
                    # Clean display of found address
                    formatted_address = geocode_result['formatted_address']
                    st.markdown(f"**📍 Found:** {formatted_address}")
                    
                    # Show basic property info
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Latitude:** {geocode_result['lat']:.6f}")
                    with col2:
                        st.write(f"**Longitude:** {geocode_result['lng']:.6f}")
                    
                    return {
                        'address': formatted_address,
                        'coordinates': {
                            'lat': geocode_result['lat'],
                            'lng': geocode_result['lng']
                        },
                        'place_id': geocode_result.get('place_id', ''),
                        'status': 'success'
                    }
                
                elif geocode_result['status'] == 'error':
                    st.error(f"❌ Could not find address: {geocode_result['message']}")
                    st.info("💡 Try entering a more complete address with city and state")
                    return None
        else:
            # Show helpful suggestions as user types
            st.info("💡 Keep typing your complete address...")
            return None
    
    elif address_input and len(address_input) <= 5:
        st.info("💡 Enter a complete address to begin solar analysis")
        return None
    
    else:
        # Landing state like Project Sunroof
        st.markdown("""
        **Example addresses to try:**
        - 1600 Amphitheatre Parkway, Mountain View, CA
        - 350 5th Ave, New York, NY 10118
        - 1 Apple Park Way, Cupertino, CA
        """)
        return None

def render_address_suggestions(query: str) -> None:
    """Render address autocomplete suggestions (placeholder for full implementation)"""
    
    # In a full implementation, this would use Google Places Autocomplete API
    # For now, we'll just show the basic geocoding result
    
    if len(query) > 5:
        st.write("💡 **Suggestion:** Try entering a complete address like '123 Main St, City, State, ZIP'")
