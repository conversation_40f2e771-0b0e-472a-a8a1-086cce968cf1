import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict
from datetime import datetime

def render_key_metrics_header(analysis_data: Dict):
    """Render key metrics header like Project Sunroof"""
    
    st.subheader("☀️ Solar Analysis Summary")
    
    roof_analysis = analysis_data.get('roof_analysis', {})
    system_estimates = analysis_data.get('system_estimates', {})
    
    # Large metrics like Project Sunroof
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Usable Sunlight",
            f"{system_estimates.get('annual_sunlight_hours', 0):,.0f} hrs/year",
            help="Hours of usable sunlight per year"
        )
    
    with col2:
        st.metric(
            "Roof Area",
            f"{roof_analysis.get('usable_area_sqft', 0):,.0f} sq ft",
            help="Available roof area for solar panels"
        )
    
    with col3:
        st.metric(
            "Annual Savings",
            f"${system_estimates.get('annual_savings', 0):,.0f}",
            help="Estimated annual electricity savings"
        )
    
    with col4:
        st.metric(
            "20-Year Savings",
            f"${system_estimates.get('savings_20_years', 0):,.0f}",
            help="Total estimated savings over 20 years"
        )

def render_results_display(analysis_data: Dict):
    """Render comprehensive analysis results in organized tabs"""
    
    # Create main result tabs
    result_tabs = st.tabs(["📊 Main Analysis", "🔧 Technical Data", "💡 Recommendations"])
    
    with result_tabs[0]:
        render_main_analysis(analysis_data)
    
    with result_tabs[1]:
        render_technical_data(analysis_data)
    
    with result_tabs[2]:
        render_recommendations(analysis_data)

def render_main_analysis(analysis_data: Dict):
    """Render main analysis results"""
    
    st.subheader("🏠 Property Solar Analysis")
    
    # Analysis header with key metrics
    roof_analysis = analysis_data.get('roof_analysis', {})
    system_estimates = analysis_data.get('system_estimates', {})
    
    # Key metrics cards
    metric_cols = st.columns(5)
    
    with metric_cols[0]:
        suitability_score = roof_analysis.get('solar_suitability_score', 0)
        score_color = get_suitability_color(suitability_score)
        st.metric(
            "Solar Suitability", 
            f"{suitability_score}/10",
            help="Overall solar installation suitability rating"
        )
        st.markdown(f"<div style='text-align: center; color: {score_color}; font-weight: bold;'>{get_suitability_label(suitability_score)}</div>", unsafe_allow_html=True)
    
    with metric_cols[1]:
        st.metric(
            "System Size",
            f"{system_estimates.get('system_capacity_kw', 0):.1f} kW",
            help="Recommended solar system capacity"
        )
    
    with metric_cols[2]:
        st.metric(
            "Annual Generation",
            f"{system_estimates.get('annual_generation_kwh', 0):,.0f} kWh",
            help="Expected annual energy production"
        )
    
    with metric_cols[3]:
        st.metric(
            "Annual Savings",
            f"${system_estimates.get('annual_savings', 0):,.0f}",
            help="Estimated annual electricity bill savings"
        )
    
    with metric_cols[4]:
        st.metric(
            "Payback Period",
            f"{system_estimates.get('payback_period', 0):.1f} years",
            help="Time to recover initial investment"
        )
    
    st.divider()
    
    # Roof assessment section
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.write("**🏠 Roof Assessment**")
        
        roof_metrics = {
            "Usable Area": f"{roof_analysis.get('usable_area_sqft', 0):,.0f} sq ft",
            "Roof Condition": roof_analysis.get('roof_condition', 'N/A'),
            "Orientation": roof_analysis.get('roof_orientation', 'N/A'),
            "Shading": roof_analysis.get('shading_assessment', 'N/A'),
            "Structure": roof_analysis.get('structural_assessment', 'N/A')
        }
        
        for metric, value in roof_metrics.items():
            st.write(f"**{metric}:** {value}")
    
    with col2:
        st.write("**⚡ System Overview**")
        
        system_metrics = {
            "Solar Panels": f"{system_estimates.get('max_panels', 0)} panels",
            "System Cost": f"${system_estimates.get('system_cost', 0):,.0f}",
            "Tax Credit": f"${system_estimates.get('federal_tax_credit', 0):,.0f}",
            "Net Investment": f"${system_estimates.get('net_system_cost', 0):,.0f}",
            "20-Year Savings": f"${system_estimates.get('savings_20_years', 0):,.0f}"
        }
        
        for metric, value in system_metrics.items():
            st.write(f"**{metric}:** {value}")
    
    # Financial projection chart
    st.write("**💰 Financial Projection**")
    render_financial_chart(system_estimates, analysis_data.get('parameters', {}))
    
    # Environmental impact
    st.write("**🌱 Environmental Impact**")
    render_environmental_impact(system_estimates)

def render_technical_data(analysis_data: Dict):
    """Render detailed technical data"""
    
    st.subheader("🔧 Technical Analysis Details")
    
    # Solar resource data
    solar_data = analysis_data.get('solar_data', {})
    elevation_data = analysis_data.get('elevation', {})
    parameters = analysis_data.get('parameters', {})
    system_estimates = analysis_data.get('system_estimates', {})
    
    # Technical specifications table
    st.write("**📋 Technical Specifications**")
    
    tech_specs = {
        "Parameter": [
            "Property Elevation",
            "Annual Sunlight Hours",
            "Solar Irradiance",
            "Panel Efficiency",
            "System Losses",
            "Electricity Rate",
            "Capacity Factor",
            "System Degradation"
        ],
        "Value": [
            f"{elevation_data.get('elevation_feet', 0):,.0f} ft ({elevation_data.get('elevation_meters', 0):.0f} m)",
            f"{system_estimates.get('annual_sunlight_hours', 0):,.0f} hours/year",
            f"{solar_data.get('annual_irradiance_kwh_per_m2', 0):,.0f} kWh/m²/year",
            f"{parameters.get('panel_efficiency', 20)}%",
            f"{parameters.get('system_losses', 14)}%",
            f"${parameters.get('electricity_rate', 0.12):.3f}/kWh",
            "15% (typical residential)",
            "0.5% per year"
        ],
        "Description": [
            "Property elevation above sea level",
            "Total usable sunlight hours annually",
            "Annual solar energy potential per square meter",
            "Solar panel conversion efficiency",
            "System losses (inverter, wiring, etc.)",
            "Current electricity rate used in calculations",
            "Actual output vs. maximum theoretical output",
            "Annual system performance degradation"
        ]
    }
    
    tech_df = pd.DataFrame(tech_specs)
    st.dataframe(tech_df, use_container_width=True, hide_index=True)
    
    # System design details
    st.write("**⚙️ System Design**")
    
    system_estimates = analysis_data.get('system_estimates', {})
    roof_analysis = analysis_data.get('roof_analysis', {})
    
    design_cols = st.columns(2)
    
    with design_cols[0]:
        st.write("**Panel Configuration**")
        st.write(f"• Number of Panels: {system_estimates.get('max_panels', 0)}")
        st.write(f"• Panel Wattage: 400W each (typical)")
        st.write(f"• Total DC Capacity: {system_estimates.get('system_capacity_kw', 0):.1f} kW")
        st.write(f"• Roof Coverage: {(system_estimates.get('max_panels', 0) * 22):,.0f} sq ft")
        
    with design_cols[1]:
        st.write("**Performance Estimates**")
        st.write(f"• Annual Generation: {system_estimates.get('annual_generation_kwh', 0):,.0f} kWh")
        st.write(f"• Daily Average: {system_estimates.get('annual_generation_kwh', 0) / 365:.1f} kWh")
        st.write(f"• Peak Summer: {system_estimates.get('annual_generation_kwh', 0) * 1.25 / 365:.1f} kWh/day")
        st.write(f"• Winter Minimum: {system_estimates.get('annual_generation_kwh', 0) * 0.65 / 365:.1f} kWh/day")
    
    # API status and data sources
    st.write("**📡 Data Sources & API Status**")
    
    api_status = []
    
    # Check satellite imagery status
    satellite_status = analysis_data.get('satellite_imagery', {}).get('status', 'unknown')
    api_status.append(("Google Maps Static API", satellite_status, "Satellite imagery"))
    
    # Check solar data status
    solar_status = solar_data.get('status', 'unknown')
    api_status.append(("Google Solar API", solar_status, "Solar irradiance data"))
    
    # Check elevation data status
    elevation_status = elevation_data.get('status', 'unknown')
    api_status.append(("Google Elevation API", elevation_status, "Elevation information"))
    
    # Check roof analysis status
    roof_status = roof_analysis.get('status', 'unknown')
    api_status.append(("Gemini AI Analysis", roof_status, "Roof assessment"))
    
    status_df = pd.DataFrame(api_status)
    status_df.columns = ["API Service", "Status", "Data Type"]
    
    # Color code status
    def color_status(val):
        if val == 'success':
            return 'background-color: #d4edda; color: #155724'
        elif val == 'fallback':
            return 'background-color: #fff3cd; color: #856404'
        elif val == 'error':
            return 'background-color: #f8d7da; color: #721c24'
        else:
            return ''
    
    st.dataframe(
        status_df.style.map(color_status, subset=['Status']),
        use_container_width=True,
        hide_index=True
    )

def render_recommendations(analysis_data: Dict):
    """Render AI-generated and general recommendations"""
    
    st.subheader("💡 Recommendations & Next Steps")
    
    roof_analysis = analysis_data.get('roof_analysis', {})
    system_estimates = analysis_data.get('system_estimates', {})
    
    # AI recommendations
    ai_recommendations = roof_analysis.get('recommendations', [])
    
    if ai_recommendations:
        st.write("**🤖 AI Analysis Recommendations**")
        
        for i, recommendation in enumerate(ai_recommendations, 1):
            st.write(f"{i}. {recommendation}")
        
        st.divider()
    
    # Suitability-based recommendations
    suitability_score = roof_analysis.get('solar_suitability_score', 0)
    
    if suitability_score >= 8:
        st.success("✅ **Excellent Solar Potential** - Your property is highly suitable for solar installation!")
        priority_recommendations = [
            "Proceed with obtaining quotes from certified solar installers",
            "Schedule professional site assessment to confirm analysis",
            "Research local and state incentives to maximize savings",
            "Consider larger system size if budget allows for maximum benefit"
        ]
    elif suitability_score >= 6:
        st.info("👍 **Good Solar Potential** - Your property shows solid potential for solar installation.")
        priority_recommendations = [
            "Get professional assessment to address any concerns identified",
            "Consider tree trimming if shading is an issue",
            "Evaluate roof condition and plan any needed repairs before installation",
            "Compare financing options to find the best fit"
        ]
    else:
        st.warning("⚠️ **Limited Solar Potential** - Consider these factors before proceeding.")
        priority_recommendations = [
            "Address shading issues if possible (tree trimming, etc.)",
            "Consider roof improvements or repairs",
            "Evaluate ground-mount or community solar options",
            "Reassess in the future if property conditions change"
        ]
    
    st.write("**🎯 Priority Actions**")
    for i, rec in enumerate(priority_recommendations, 1):
        st.write(f"{i}. {rec}")
    
    st.divider()
    
    # General next steps
    st.write("**📋 Detailed Next Steps**")
    
    next_steps_tabs = st.tabs(["🔍 Assessment", "💰 Financing", "📜 Permits", "🏗️ Installation"])
    
    with next_steps_tabs[0]:
        st.write("**Professional Site Assessment**")
        st.write("""
        • **Structural Engineering**: Verify roof can support solar panel weight
        • **Electrical Assessment**: Ensure electrical panel can handle solar integration  
        • **Detailed Measurements**: Confirm available space and optimal panel layout
        • **Shading Analysis**: Time-of-day and seasonal shading assessment
        • **Roof Condition**: Verify roof age and condition, plan any needed repairs
        """)
        
        st.write("**Questions to Ask Installers:**")
        st.write("""
        • What equipment brands do you recommend and why?
        • What warranties are included (equipment, workmanship, performance)?
        • How do you handle permits and utility interconnection?
        • What is your installation timeline?
        • Can you provide local references?
        """)
    
    with next_steps_tabs[1]:
        st.write("**Financing Options**")
        
        financing_options = {
            "Cash Purchase": {
                "Pros": "Highest savings, full ownership, all incentives",
                "Cons": "High upfront cost",
                "Best For": "Those with available capital"
            },
            "Solar Loan": {
                "Pros": "No upfront cost, ownership benefits, tax credits",
                "Cons": "Interest payments",
                "Best For": "Most homeowners"
            },
            "Solar Lease": {
                "Pros": "No upfront cost, maintenance included",
                "Cons": "Lower savings, no ownership",
                "Best For": "Those wanting simple solution"
            },
            "Power Purchase Agreement (PPA)": {
                "Pros": "No upfront cost, predictable rates",
                "Cons": "Lower savings, complex contracts",
                "Best For": "Risk-averse homeowners"
            }
        }
        
        for option, details in financing_options.items():
            with st.expander(f"💳 {option}"):
                st.write(f"**Pros:** {details['Pros']}")
                st.write(f"**Cons:** {details['Cons']}")
                st.write(f"**Best For:** {details['Best For']}")
    
    with next_steps_tabs[2]:
        st.write("**Permits and Approvals**")
        st.write("""
        **Required Permits (typically handled by installer):**
        • Building permit from local authority
        • Electrical permit for system connection
        • Utility interconnection agreement
        • HOA approval (if applicable)
        
        **Timeline:** Usually 2-8 weeks depending on local processes
        
        **Your Role:** Provide required documentation and signatures
        """)
    
    with next_steps_tabs[3]:
        st.write("**Installation Process**")
        st.write("""
        **Typical Installation Timeline:**
        
        **Week 1:** Site preparation and safety setup
        • Install safety equipment and scaffolding
        • Mark panel and equipment locations
        
        **Week 1-2:** Electrical and mounting work
        • Install racking system on roof
        • Run electrical conduits and wiring
        • Install inverter and monitoring equipment
        
        **Week 2:** Panel installation and testing
        • Mount solar panels on racking system
        • Complete electrical connections
        • Test system operation and safety
        
        **Week 3-4:** Inspection and activation
        • Local building inspection
        • Utility inspection and meter installation
        • System commissioning and monitoring setup
        """)
    
    # Cost-saving tips
    st.divider()
    st.write("**💰 Cost-Saving Tips**")
    
    savings_tips = [
        "**Get Multiple Quotes**: Compare at least 3 certified installers",
        "**Research Incentives**: Check for state, local, and utility rebates",
        "**Time Installation**: Consider seasonal pricing variations",
        "**System Size**: Right-size your system based on actual usage",
        "**Equipment Selection**: Balance cost, quality, and warranty",
        "**Roof Preparation**: Address any roof issues before installation"
    ]
    
    for tip in savings_tips:
        st.write(f"• {tip}")

def render_financial_chart(system_estimates: Dict, parameters: Dict):
    """Render financial projection chart"""
    
    years = list(range(0, 21))
    annual_savings = system_estimates.get('annual_savings', 0)
    net_investment = system_estimates.get('net_system_cost', 0)
    
    # Calculate cumulative cash flow
    cumulative_cash_flow = [-net_investment]  # Initial investment
    
    for year in range(1, 21):
        # Account for electricity rate increases and system degradation
        year_savings = annual_savings * (1.025 ** (year - 1)) * (0.995 ** (year - 1))
        cumulative_cash_flow.append(cumulative_cash_flow[-1] + year_savings)
    
    # Create chart
    fig = go.Figure()
    
    # Add cumulative cash flow
    fig.add_trace(go.Scatter(
        x=years,
        y=cumulative_cash_flow,
        mode='lines+markers',
        name='Cumulative Cash Flow',
        line=dict(color='green', width=3),
        fill='tonexty' if min(cumulative_cash_flow) < 0 else 'tozeroy',
        fillcolor='rgba(0,255,0,0.1)'
    ))
    
    # Add break-even line
    fig.add_hline(y=0, line_dash="dash", line_color="red", annotation_text="Break-even")
    
    # Mark payback period
    payback_period = system_estimates.get('payback_period', 0)
    if payback_period > 0 and payback_period <= 20:
        fig.add_vline(
            x=payback_period, 
            line_dash="dot", 
            line_color="blue",
            annotation_text=f"Payback: {payback_period:.1f} years"
        )
    
    fig.update_layout(
        title="20-Year Financial Projection",
        xaxis_title="Year",
        yaxis_title="Cumulative Cash Flow ($)",
        hovermode='x unified',
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def render_environmental_impact(system_estimates: Dict):
    """Render environmental impact metrics"""
    
    annual_generation = system_estimates.get('annual_generation_kwh', 0)
    
    # Environmental calculations
    co2_avoided_annual = annual_generation * 0.0004  # tons CO2 per kWh (US average)
    co2_avoided_20_years = co2_avoided_annual * 20
    trees_equivalent = co2_avoided_20_years * 40  # trees per ton CO2
    cars_equivalent = co2_avoided_annual * 0.25  # cars off road per ton CO2 annually
    
    env_cols = st.columns(4)
    
    with env_cols[0]:
        st.metric(
            "CO₂ Avoided (Annual)",
            f"{co2_avoided_annual:.1f} tons",
            help="Annual carbon dioxide emissions avoided"
        )
    
    with env_cols[1]:
        st.metric(
            "CO₂ Avoided (20 years)",
            f"{co2_avoided_20_years:.0f} tons",
            help="Total CO₂ avoided over system lifetime"
        )
    
    with env_cols[2]:
        st.metric(
            "Trees Planted Equivalent",
            f"{trees_equivalent:.0f} trees",
            help="Equivalent environmental benefit of planting trees"
        )
    
    with env_cols[3]:
        st.metric(
            "Cars Off Road",
            f"{cars_equivalent:.1f} cars/year",
            help="Equivalent to removing cars from the road annually"
        )

def get_suitability_color(score: int) -> str:
    """Get color based on suitability score"""
    if score >= 8:
        return "#28a745"  # Green
    elif score >= 6:
        return "#ffc107"  # Yellow
    else:
        return "#dc3545"  # Red

def get_suitability_label(score: int) -> str:
    """Get label based on suitability score"""
    if score >= 8:
        return "Excellent"
    elif score >= 6:
        return "Good"
    else:
        return "Limited"
