☀️ Solar Roof Analyzer
Comprehensive solar potential assessment powered by Google APIs and Gemini AI

🏠 Property Solar Analysis
Find your home's solar potential
Search for your address to discover how much you could save with solar panels.

123 henry street
✅ Property located!

📍 Found: 123 Henry St, Brooklyn, NY 11201, USA

Latitude: 40.696925

Longitude: -73.993072


☀️ Solar Analysis Summary
Usable Sunlight

1,467 hrs/year
Roof Area

1,500 sq ft
Annual Savings

$3,688
20-Year Savings

$16,649
🗺️ Solar Analysis Map
Your Property's Solar Potential
0
Solar Suitability Analysis

Legend

0
Image Details

📍 Location: 40.696925, -73.993072

🔍 Zoom Level: 20

📏 Resolution: 800x800 pixels



Quick Stats

🔴 Limited (4/10)

📐 Usable Area: 1,500 sq ft

🏠 Condition: Fair

🧭 Orientation: Mixed

🔍 Analysis Summary

Usable Area

1,500 sq ft
Suitability Score

4/10
Roof Condition

Fair
Orientation

Mixed
📋 Detailed Assessment

📊 Additional Analysis

🏠 Street View


📊 Solar Metrics


📈 Energy Production

🏠 Street View Analysis
Multi-angle roof visualization for comprehensive assessment

0
North View (0°)

0
East View (90°)

0
South View (180°)

0
West View (270°)

🔍 Street View Insights

Street View imagery helps identify: • Roof accessibility and structural details • Nearby obstructions (trees, buildings) • Roof angle and orientation • Potential shading sources • Overall property condition

📊 Detailed Analysis

📊 Main Analysis


🔧 Technical Data


💡 Recommendations

🏠 Property Solar Analysis
Solar Suitability

4/10
Limited
System Size

27.2 kW
Annual Generation

30,737 kWh
Annual Savings

$3,688
Payback Period

15.5 years
🏠 Roof Assessment

Usable Area: 1,500 sq ft

Roof Condition: Fair

Orientation: Mixed

Shading: Significant shading from numerous large, mature trees located to the south and west of the property. These trees will likely cause substantial power loss, especially during morning and late afternoon hours, and more prominently during winter months.

Structure: The main building structure appears to be solid masonry, likely capable of supporting a solar installation. However, the northernmost flat roof section shows signs of disrepair (blue tarp), raising concerns about water integrity and the condition of the underlying structure. A full on-site structural evaluation is required.

⚡ System Overview

Solar Panels: 68 panels

System Cost: $81,600

Tax Credit: $24,480

Net Investment: $57,120

20-Year Savings: $16,649

💰 Financial Projection

🌱 Environmental Impact

CO₂ Avoided (Annual)

12.3 tons
CO₂ Avoided (20 years)

246 tons
Trees Planted Equivalent

9836 trees
Cars Off Road

3.1 cars/year

🗺️ Solar Visualization
Your Property's Solar Potential
0
Solar Suitability Analysis

Legend

0
Image Details

📍 Location: 40.696925, -73.993072

🔍 Zoom Level: 20

📏 Resolution: 800x800 pixels

Error processing satellite imagery: There are multiple elements with the same key='show_original'. To fix this, please make sure that the key argument is unique for each element you create.

0
Satellite Image

📊 Additional Analysis

🏠 Street View


📊 Solar Metrics


📈 Energy Production

🏠 Street View Analysis
Multi-angle roof visualization for comprehensive assessment

0
North View (0°)

0
East View (90°)

0
South View (180°)

0
West View (270°)

🔍 Street View Insights

Street View imagery helps identify: • Roof accessibility and structural details • Nearby obstructions (trees, buildings) • Roof angle and orientation • Potential shading sources • Overall property condition

streamlit.errors.StreamlitDuplicateElementKey: There are multiple elements with the same key='solar_radar_chart'. To fix this, please make sure that the key argument is unique for each element you create.

Traceback:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/exec_code.py", line 128, in exec_func_with_error_handling
    result = func()
             ^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 669, in code_to_exec
    exec(code, module.__dict__)  # noqa: S102
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/app.py", line 308, in <module>
    main()
File "/home/<USER>/workspace/app.py", line 125, in main
    render_solar_visualization(st.session_state.analysis_data)
File "/home/<USER>/workspace/components/solar_visualization.py", line 23, in render_solar_visualization
    render_solar_metrics_visualization(analysis_data)
File "/home/<USER>/workspace/components/solar_visualization.py", line 262, in render_solar_metrics_visualization
    st.plotly_chart(fig, use_container_width=True, key="solar_radar_chart")
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/metrics_util.py", line 443, in wrapped_func
    result = non_optional_func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/plotly_chart.py", line 509, in plotly_chart
    plotly_chart_proto.id = compute_and_register_element_id(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/lib/utils.py", line 254, in compute_and_register_element_id
    _register_element_id(ctx, element_type, element_id)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/lib/utils.py", line 143, in _register_element_id
    raise StreamlitDuplicateElementKey(user_key)