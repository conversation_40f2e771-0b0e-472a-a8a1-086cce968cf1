# 🚀 Quick Start Guide - Solar Potential Analyzer

## 📋 Prerequisites Checklist

- [ ] Python 3.8+ installed
- [ ] Google Cloud Platform account
- [ ] Google AI Studio account
- [ ] Git installed (optional)

## ⚡ 5-Minute Setup

### 1. Download and Install
```bash
# Option A: Clone repository
git clone <repository-url>
cd SolarPotentialAnalyzer

# Option B: Download ZIP and extract
# Then navigate to the extracted folder
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Get API Keys

#### Google API Key (Required)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create/select a project
3. Enable these APIs:
   - Maps Static API
   - Geocoding API  
   - Solar API
4. Create API Key in [Credentials](https://console.cloud.google.com/apis/credentials)

#### Gemini API Key (Required)
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Click "Create API Key"
3. Copy the key

### 4. Configure Environment
```bash
# Copy the template
cp .env.example .env

# Edit .env file and add your keys:
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
```

### 5. Run the Application
```bash
# Option A: Automated setup (Recommended)
python run_local.py

# Option B: Direct launch
streamlit run app.py
```

## 🎯 First Analysis

1. **Open Browser**: Navigate to `http://localhost:8501`
2. **Enter Address**: Type any address (e.g., "1600 Amphitheatre Parkway, Mountain View, CA")
3. **Adjust Settings**: 
   - Panel Efficiency: 20%
   - System Losses: 14%
   - Electricity Rate: $0.12/kWh
   - Zoom Level: 20
4. **Click "Analyze Solar Potential"**
5. **View Results**: Enhanced solar panel mapping with energy-based colors!

## 🎨 What You'll See

### Enhanced Solar Panel Mapping
- **Green panels**: High energy production (2500+ kWh/year)
- **Yellow panels**: Medium energy production (1500-2500 kWh/year)  
- **Red panels**: Low energy production (<1500 kWh/year)

### Comprehensive Analysis
- High-resolution satellite imagery
- Accurate panel placement using real coordinates
- Financial projections and ROI calculations
- Interactive charts and metrics

## 🧪 Test Without API Keys

Run the offline demo to see the enhanced functionality:
```bash
python demo_offline.py
```

This creates sample visualizations showing:
- Energy-based color coding
- Coordinate conversion accuracy
- Panel orientation handling
- Composite visualization pipeline

## 🔧 Troubleshooting

### Common Issues

**"API key not found"**
```bash
# Check your .env file exists and has correct format
cat .env
```

**"Module not found"**
```bash
# Reinstall dependencies
pip install -r requirements.txt
```

**"Permission denied"**
```bash
# On Windows, try:
python -m streamlit run app.py

# On Mac/Linux, ensure execute permissions:
chmod +x run_local.py
```

### Performance Tips
- Use zoom level 20 for best balance
- Ensure stable internet connection
- Close other browser tabs for better performance

## 📊 Sample Addresses to Try

**High Solar Potential:**
- "1600 Amphitheatre Parkway, Mountain View, CA" (Google HQ)
- "1 Tesla Road, Austin, TX" (Tesla Gigafactory)
- "Phoenix, AZ" (Desert climate)

**Mixed Solar Potential:**
- "Seattle, WA" (Cloudy climate)
- "New York, NY" (Urban environment)
- "Miami, FL" (Tropical climate)

## 🎯 Key Features to Explore

### 1. Enhanced Solar Panel Mapping
- Notice how panels are colored by energy production
- See accurate positioning based on real coordinates
- Observe proper panel orientation and rotation

### 2. Interactive Controls
- Toggle between satellite and hybrid views
- Adjust analysis parameters in real-time
- Export results as PDF reports

### 3. Professional Visualizations
- Project Sunroof-quality mapping
- GeoTiff-style heatmap overlays
- Comprehensive financial analysis

## 📈 Next Steps

1. **Analyze Your Property**: Enter your home address
2. **Customize Parameters**: Adjust settings for your local conditions
3. **Generate Report**: Export professional PDF analysis
4. **Compare Locations**: Try different addresses to compare solar potential

## 🆘 Need Help?

- **Documentation**: See `README.md` for detailed setup
- **Technical Details**: Check `ENHANCED_SOLAR_MAPPING.md`
- **Test Suite**: Run `python test_image_processor_only.py`
- **Demo Mode**: Use `python demo_offline.py` for offline testing

---

**🌟 You're now ready to analyze solar potential with Project Sunroof-quality visualizations!**
