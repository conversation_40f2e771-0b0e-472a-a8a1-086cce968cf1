from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import PageBreak
from reportlab.lib import colors
from reportlab.graphics.shapes import Drawing, Rect
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics import renderPDF
import io
from datetime import datetime
import os

class PDFGenerator:
    """Professional PDF report generator for solar analysis"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for the PDF"""
        
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            textColor=HexColor('#1f4e79'),
            alignment=1  # Center alignment
        ))
        
        # Heading style
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            textColor=HexColor('#2e75b6'),
            borderWidth=1,
            borderColor=HexColor('#2e75b6'),
            borderPadding=5
        ))
        
        # Subheading style
        self.styles.add(ParagraphStyle(
            name='CustomSubheading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            textColor=HexColor('#c55a11')
        ))
    
    def generate_report(self, analysis_data):
        """Generate comprehensive PDF report"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build story (content)
        story = []
        
        # Title page
        story.extend(self._create_title_page(analysis_data))
        story.append(PageBreak())
        
        # Executive summary
        story.extend(self._create_executive_summary(analysis_data))
        story.append(PageBreak())
        
        # Technical analysis
        story.extend(self._create_technical_analysis(analysis_data))
        story.append(PageBreak())
        
        # Financial analysis
        story.extend(self._create_financial_analysis(analysis_data))
        story.append(PageBreak())
        
        # Recommendations
        story.extend(self._create_recommendations(analysis_data))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        
        return buffer
    
    def _create_title_page(self, analysis_data):
        """Create title page"""
        
        story = []
        
        # Title
        story.append(Paragraph("☀️ Solar Roof Analysis Report", self.styles['CustomTitle']))
        story.append(Spacer(1, 20))
        
        # Address
        address = analysis_data.get('address', 'Address Not Available')
        story.append(Paragraph(f"<b>Property Address:</b><br/>{address}", self.styles['Normal']))
        story.append(Spacer(1, 30))
        
        # Report date
        report_date = datetime.now().strftime("%B %d, %Y")
        story.append(Paragraph(f"<b>Report Generated:</b> {report_date}", self.styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Key metrics table
        system_estimates = analysis_data.get('system_estimates', {})
        
        key_metrics = [
            ['Metric', 'Value'],
            ['Estimated System Size', f"{system_estimates.get('system_capacity_kw', 0):.1f} kW"],
            ['Annual Energy Generation', f"{system_estimates.get('annual_generation_kwh', 0):,.0f} kWh"],
            ['Estimated Annual Savings', f"${system_estimates.get('annual_savings', 0):,.0f}"],
            ['Payback Period', f"{system_estimates.get('payback_period', 0):.1f} years"],
            ['20-Year Net Savings', f"${system_estimates.get('savings_20_years', 0):,.0f}"]
        ]
        
        table = Table(key_metrics, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#2e75b6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f2f2f2')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        return story
    
    def _create_executive_summary(self, analysis_data):
        """Create executive summary section"""
        
        story = []
        
        story.append(Paragraph("Executive Summary", self.styles['CustomHeading']))
        
        roof_analysis = analysis_data.get('roof_analysis', {})
        system_estimates = analysis_data.get('system_estimates', {})
        
        # Solar suitability
        suitability_score = roof_analysis.get('solar_suitability_score', 0)
        if suitability_score >= 8:
            suitability_text = "Excellent"
            suitability_color = "green"
        elif suitability_score >= 6:
            suitability_text = "Good"
            suitability_color = "orange"
        else:
            suitability_text = "Limited"
            suitability_color = "red"
        
        summary_text = f"""
        <b>Solar Suitability:</b> <span color="{suitability_color}">{suitability_text} ({suitability_score}/10)</span><br/><br/>
        
        Your property shows {suitability_text.lower()} potential for solar panel installation. 
        Based on our comprehensive analysis using satellite imagery and AI-powered roof assessment, 
        we have identified approximately {roof_analysis.get('usable_area_sqft', 0):,.0f} square feet of usable roof area.<br/><br/>
        
        <b>Key Findings:</b><br/>
        • Roof condition: {roof_analysis.get('roof_condition', 'N/A')}<br/>
        • Primary roof orientation: {roof_analysis.get('roof_orientation', 'N/A')}<br/>
        • Recommended system size: {system_estimates.get('system_capacity_kw', 0):.1f} kW<br/>
        • Estimated annual generation: {system_estimates.get('annual_generation_kwh', 0):,.0f} kWh<br/>
        • Annual electricity savings: ${system_estimates.get('annual_savings', 0):,.0f}<br/><br/>
        
        <b>Financial Overview:</b><br/>
        The recommended solar system would cost approximately ${system_estimates.get('system_cost', 0):,.0f} before incentives. 
        After applying the federal tax credit, your net investment would be ${system_estimates.get('net_system_cost', 0):,.0f}. 
        Based on your local electricity rates, the system would pay for itself in approximately {system_estimates.get('payback_period', 0):.1f} years 
        and generate ${system_estimates.get('savings_20_years', 0):,.0f} in net savings over 20 years.
        """
        
        story.append(Paragraph(summary_text, self.styles['Normal']))
        
        return story
    
    def _create_technical_analysis(self, analysis_data):
        """Create technical analysis section"""
        
        story = []
        
        story.append(Paragraph("Technical Analysis", self.styles['CustomHeading']))
        
        # Roof assessment
        story.append(Paragraph("Roof Assessment", self.styles['CustomSubheading']))
        
        roof_analysis = analysis_data.get('roof_analysis', {})
        solar_data = analysis_data.get('solar_data', {})
        elevation_data = analysis_data.get('elevation', {})
        
        roof_details = [
            ['Parameter', 'Value', 'Assessment'],
            ['Usable Roof Area', f"{roof_analysis.get('usable_area_sqft', 0):,.0f} sq ft", 'Available for panels'],
            ['Roof Condition', roof_analysis.get('roof_condition', 'N/A'), 'Structural suitability'],
            ['Roof Orientation', roof_analysis.get('roof_orientation', 'N/A'), 'Sun exposure direction'],
            ['Shading Assessment', roof_analysis.get('shading_assessment', 'N/A'), 'Impact on generation'],
            ['Elevation', f"{elevation_data.get('elevation_feet', 0):,.0f} ft", 'Above sea level'],
            ['Annual Sunlight Hours', f"{solar_data.get('annual_sunlight_hours', 0):,.0f} hours", 'Solar resource availability']
        ]
        
        table = Table(roof_details, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#c55a11')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f9f9f9')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # System specifications
        story.append(Paragraph("Recommended System Specifications", self.styles['CustomSubheading']))
        
        system_estimates = analysis_data.get('system_estimates', {})
        parameters = analysis_data.get('parameters', {})
        
        system_specs = [
            ['Specification', 'Value'],
            ['Number of Solar Panels', f"{system_estimates.get('max_panels', 0)} panels"],
            ['Total System Capacity', f"{system_estimates.get('system_capacity_kw', 0):.1f} kW DC"],
            ['Panel Efficiency', f"{parameters.get('panel_efficiency', 20)}%"],
            ['System Losses', f"{parameters.get('system_losses', 14)}%"],
            ['Expected Annual Generation', f"{system_estimates.get('annual_generation_kwh', 0):,.0f} kWh"],
            ['Capacity Factor', "15% (typical residential)"],
            ['System Lifespan', "25+ years (warranted)"]
        ]
        
        table = Table(system_specs, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#2e75b6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f2f2f2')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        return story
    
    def _create_financial_analysis(self, analysis_data):
        """Create financial analysis section"""
        
        story = []
        
        story.append(Paragraph("Financial Analysis", self.styles['CustomHeading']))
        
        system_estimates = analysis_data.get('system_estimates', {})
        parameters = analysis_data.get('parameters', {})
        
        # Cost breakdown
        story.append(Paragraph("Investment and Incentives", self.styles['CustomSubheading']))
        
        cost_breakdown = [
            ['Item', 'Amount'],
            ['Gross System Cost', f"${system_estimates.get('system_cost', 0):,.0f}"],
            ['Federal Tax Credit (30%)', f"-${system_estimates.get('federal_tax_credit', 0):,.0f}"],
            ['Net Investment', f"${system_estimates.get('net_system_cost', 0):,.0f}"],
            ['', ''],
            ['Annual Electricity Savings', f"${system_estimates.get('annual_savings', 0):,.0f}"],
            ['Simple Payback Period', f"{system_estimates.get('payback_period', 0):.1f} years"],
            ['20-Year Net Savings', f"${system_estimates.get('savings_20_years', 0):,.0f}"]
        ]
        
        table = Table(cost_breakdown, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#2e75b6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f2f2f2')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 6), (-1, 6), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 6), (-1, 6), HexColor('#d4edda'))
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # Assumptions
        story.append(Paragraph("Financial Assumptions", self.styles['CustomSubheading']))
        
        assumptions_text = f"""
        <b>Key assumptions used in this financial analysis:</b><br/><br/>
        • Electricity rate: ${parameters.get('electricity_rate', 0.12):.3f} per kWh<br/>
        • Annual electricity rate escalation: 2.5%<br/>
        • Federal tax credit: 30% (current policy)<br/>
        • System degradation: 0.5% per year<br/>
        • Analysis period: 20 years<br/>
        • Financing: Cash purchase (no loan interest)<br/><br/>
        
        <i>Note: Actual savings may vary based on changes in electricity rates, 
        weather patterns, system performance, and policy changes. State and local 
        incentives may be available to further reduce costs.</i>
        """
        
        story.append(Paragraph(assumptions_text, self.styles['Normal']))
        
        return story
    
    def _create_recommendations(self, analysis_data):
        """Create recommendations section"""
        
        story = []
        
        story.append(Paragraph("Recommendations", self.styles['CustomHeading']))
        
        roof_analysis = analysis_data.get('roof_analysis', {})
        system_estimates = analysis_data.get('system_estimates', {})
        
        # AI recommendations
        ai_recommendations = roof_analysis.get('recommendations', [])
        if ai_recommendations:
            story.append(Paragraph("AI Analysis Recommendations", self.styles['CustomSubheading']))
            
            for i, rec in enumerate(ai_recommendations, 1):
                story.append(Paragraph(f"{i}. {rec}", self.styles['Normal']))
            
            story.append(Spacer(1, 15))
        
        # General recommendations
        story.append(Paragraph("Next Steps", self.styles['CustomSubheading']))
        
        next_steps = """
        <b>Based on this analysis, we recommend the following steps:</b><br/><br/>
        
        <b>1. Professional Site Assessment</b><br/>
        Schedule an on-site evaluation with a certified solar installer to verify roof condition, 
        structural integrity, and electrical system compatibility.<br/><br/>
        
        <b>2. Obtain Multiple Quotes</b><br/>
        Get quotes from at least 3 certified solar installers to compare pricing, 
        equipment options, and warranty terms.<br/><br/>
        
        <b>3. Review Financing Options</b><br/>
        Explore solar loans, leases, and power purchase agreements (PPAs) in addition 
        to cash purchase to determine the best financial option for your situation.<br/><br/>
        
        <b>4. Check Local Incentives</b><br/>
        Research state, local, and utility incentives that may be available in your area 
        to further reduce the cost of your solar installation.<br/><br/>
        
        <b>5. Permit and Interconnection</b><br/>
        Work with your chosen installer to handle necessary permits and utility 
        interconnection agreements.<br/><br/>
        
        <b>Important Notes:</b><br/>
        • This analysis is preliminary and based on satellite imagery and AI assessment<br/>
        • Actual system design and performance may vary<br/>
        • Professional engineering assessment recommended before installation<br/>
        • Consider roof age and condition - replacement may be needed before solar installation
        """
        
        story.append(Paragraph(next_steps, self.styles['Normal']))
        
        return story
