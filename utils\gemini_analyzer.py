import json
import logging
import os
from typing import Dict, List, Optional
import streamlit as st

from google import genai
from google.genai import types
from pydantic import BaseModel

class RoofAnalysis(BaseModel):
    """Structured roof analysis response"""
    usable_area_sqft: float
    roof_condition: str
    roof_orientation: str
    shading_assessment: str
    solar_suitability_score: int
    panel_placement_zones: List[str]
    structural_assessment: str
    recommendations: List[str]

class GeminiAnalyzer:
    """AI-powered roof analysis using Gemini"""
    
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY", "")
        if not self.api_key:
            raise ValueError("Gemini API key not found in environment variables")
        
        self.client = genai.Client(api_key=self.api_key)
    
    def analyze_roof_image(self, image_path: str, solar_data: Dict, elevation_data: Dict) -> Dict:
        """Analyze roof image for solar potential"""
        
        try:
            # Prepare context information
            context = self._prepare_analysis_context(solar_data, elevation_data)
            
            # Analyze image with Gemini
            roof_analysis = self._perform_image_analysis(image_path, context)
            
            # Generate solar suitability zones
            suitability_zones = self._generate_suitability_zones(roof_analysis)
            
            return {
                'usable_area_sqft': roof_analysis.usable_area_sqft,
                'roof_condition': roof_analysis.roof_condition,
                'roof_orientation': roof_analysis.roof_orientation,
                'shading_assessment': roof_analysis.shading_assessment,
                'solar_suitability_score': roof_analysis.solar_suitability_score,
                'panel_placement_zones': roof_analysis.panel_placement_zones,
                'structural_assessment': roof_analysis.structural_assessment,
                'recommendations': roof_analysis.recommendations,
                'suitability_zones': suitability_zones,
                'analysis_summary': self._generate_summary(roof_analysis),
                'status': 'success'
            }
            
        except Exception as e:
            # Fallback to computer vision analysis
            return self._fallback_analysis(image_path, str(e))
    
    def _prepare_analysis_context(self, solar_data: Dict, elevation_data: Dict) -> str:
        """Prepare context information for analysis"""
        
        context = f"""
        Solar Data Context:
        - Annual irradiance: {solar_data.get('annual_irradiance_kwh_per_m2', 'N/A')} kWh/m²
        - Annual sunlight hours: {solar_data.get('annual_sunlight_hours', 'N/A')} hours
        - Elevation: {elevation_data.get('elevation_feet', 'N/A')} feet
        
        Analysis Instructions:
        1. Identify usable roof area for solar panels (exclude chimneys, vents, etc.)
        2. Assess roof condition and structural suitability
        3. Determine optimal roof orientation for solar panels
        4. Evaluate shading from trees, buildings, or other obstructions
        5. Rate solar suitability on a scale of 1-10
        6. Identify specific zones for panel placement
        7. Provide structural assessment and recommendations
        
        Please provide detailed analysis focusing on solar panel installation potential.
        """
        
        return context
    
    def _perform_image_analysis(self, image_path: str, context: str) -> RoofAnalysis:
        """Perform detailed image analysis using Gemini"""
        
        system_prompt = f"""
        You are a professional solar energy consultant analyzing a roof for solar panel installation potential.
        
        {context}
        
        Analyze the satellite image and provide a comprehensive assessment in JSON format with these fields:
        - usable_area_sqft: Estimated usable roof area in square feet
        - roof_condition: Assessment of roof condition (Excellent/Good/Fair/Poor)
        - roof_orientation: Primary roof orientation (North/South/East/West/Mixed)
        - shading_assessment: Description of shading issues
        - solar_suitability_score: Score from 1-10 (10 being ideal for solar)
        - panel_placement_zones: List of recommended zones for panel placement
        - structural_assessment: Assessment of structural suitability
        - recommendations: List of specific recommendations
        """
        
        try:
            with open(image_path, "rb") as f:
                image_bytes = f.read()
                
            response = self.client.models.generate_content(
                model="gemini-2.5-pro",
                contents=[
                    types.Part.from_bytes(
                        data=image_bytes,
                        mime_type="image/png",
                    ),
                    system_prompt
                ],
                config=types.GenerateContentConfig(
                    system_instruction=system_prompt,
                    response_mime_type="application/json",
                    response_schema=RoofAnalysis,
                ),
            )
            
            if response.text:
                data = json.loads(response.text)
                return RoofAnalysis(**data)
            else:
                raise ValueError("Empty response from Gemini")
                
        except Exception as e:
            logging.error(f"Gemini analysis failed: {e}")
            # Return fallback analysis
            return RoofAnalysis(
                usable_area_sqft=800.0,
                roof_condition="Good",
                roof_orientation="South",
                shading_assessment="Minimal shading observed",
                solar_suitability_score=7,
                panel_placement_zones=["Main roof section", "Secondary roof area"],
                structural_assessment="Appears structurally sound",
                recommendations=["Professional structural assessment recommended", "Consider panel orientation for optimal sun exposure"]
            )
    
    def _generate_suitability_zones(self, roof_analysis: RoofAnalysis) -> Dict:
        """Generate color-coded suitability zones"""
        
        # Based on suitability score and analysis
        score = roof_analysis.solar_suitability_score
        
        if score >= 8:
            primary_zone = "high_suitability"  # Green zones
            coverage = 0.8
        elif score >= 6:
            primary_zone = "medium_suitability"  # Yellow zones
            coverage = 0.6
        else:
            primary_zone = "low_suitability"  # Red zones
            coverage = 0.4
        
        return {
            'primary_zone': primary_zone,
            'coverage_percentage': coverage,
            'zone_descriptions': {
                'high_suitability': 'Optimal for solar panel installation',
                'medium_suitability': 'Suitable with minor considerations',
                'low_suitability': 'Limited suitability, requires assessment'
            }
        }
    
    def _generate_summary(self, roof_analysis: RoofAnalysis) -> str:
        """Generate human-readable analysis summary"""
        
        summary = f"""
        **Roof Analysis Summary**
        
        **Overall Suitability:** {roof_analysis.solar_suitability_score}/10
        
        **Key Findings:**
        - Usable roof area: {roof_analysis.usable_area_sqft:,.0f} sq ft
        - Roof condition: {roof_analysis.roof_condition}
        - Primary orientation: {roof_analysis.roof_orientation}
        - Shading assessment: {roof_analysis.shading_assessment}
        
        **Structural Assessment:** {roof_analysis.structural_assessment}
        
        **Recommended Panel Zones:**
        {', '.join(roof_analysis.panel_placement_zones)}
        
        **Key Recommendations:**
        {chr(10).join(['• ' + rec for rec in roof_analysis.recommendations])}
        """
        
        return summary.strip()
    
    def _fallback_analysis(self, image_path: str, error_message: str) -> Dict:
        """Provide fallback analysis when AI is unavailable"""
        
        try:
            # Basic computer vision fallback (simplified)
            import cv2
            import numpy as np
            
            # Load and analyze image
            image = cv2.imread(image_path)
            if image is not None:
                height, width = image.shape[:2]
                estimated_area = (width * height) * 0.0001  # Rough conversion to sq ft
            else:
                estimated_area = 1000  # Default estimate
            
        except ImportError:
            estimated_area = 1000  # Default if OpenCV not available
        
        return {
            'usable_area_sqft': estimated_area,
            'roof_condition': 'Assessment unavailable - AI analysis failed',
            'roof_orientation': 'Mixed',
            'shading_assessment': 'Manual assessment required',
            'solar_suitability_score': 6,
            'panel_placement_zones': ['Primary roof area'],
            'structural_assessment': 'Professional assessment recommended',
            'recommendations': [
                'AI analysis unavailable - manual assessment recommended',
                'Consult with solar professional for detailed evaluation'
            ],
            'suitability_zones': {
                'primary_zone': 'medium_suitability',
                'coverage_percentage': 0.6,
                'zone_descriptions': {
                    'medium_suitability': 'Requires manual assessment'
                }
            },
            'analysis_summary': f'**Fallback Analysis**\n\nAI analysis failed: {error_message}\n\nEstimated roof area: {estimated_area:,.0f} sq ft\n\nRecommendation: Consult with solar professional for detailed assessment.',
            'status': 'fallback',
            'error_message': error_message
        }
    
    def _perform_vision_llm_analysis(self, image_path: str, prompt: str) -> Dict:
        """Perform advanced Vision+LLM analysis for Project Sunroof-style results"""
        
        try:
            with open(image_path, "rb") as f:
                image_bytes = f.read()
                
                response = self.client.models.generate_content(
                    model="gemini-2.5-pro",
                    contents=[
                        types.Part.from_bytes(
                            data=image_bytes,
                            mime_type="image/jpeg",
                        ),
                        prompt
                    ],
                    config=types.GenerateContentConfig(
                        response_mime_type="application/json",
                        temperature=0.1,  # Low temperature for consistent, precise results
                        max_output_tokens=4000
                    )
                )
                
                if response.text:
                    try:
                        # Parse the JSON response
                        result = json.loads(response.text)
                        
                        # Validate and enhance the response
                        return self._validate_vision_llm_result(result)
                        
                    except json.JSONDecodeError as e:
                        # Try to extract JSON from the response
                        import re
                        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                        if json_match:
                            try:
                                result = json.loads(json_match.group())
                                return self._validate_vision_llm_result(result)
                            except:
                                pass
                        
                        return {
                            'status': 'error',
                            'message': f'Failed to parse LLM response: {str(e)}',
                            'raw_response': response.text[:500]
                        }
                else:
                    return {
                        'status': 'error',
                        'message': 'Empty response from Gemini Vision analysis'
                    }
                    
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Vision+LLM analysis failed: {str(e)}'
            }
    
    def _validate_vision_llm_result(self, result: Dict) -> Dict:
        """Validate and enhance Vision+LLM analysis result"""
        
        # Ensure required fields exist
        validated_result = {
            'status': 'success',
            'roof_segments': result.get('roof_segments', []),
            'solar_zones': result.get('solar_zones', []),
            'system_recommendations': result.get('system_recommendations', {}),
            'shading_analysis': result.get('shading_analysis', {}),
            'confidence': result.get('system_recommendations', {}).get('confidence_score', 75)
        }
        
        # Validate roof segments
        for segment in validated_result['roof_segments']:
            if 'coordinates' not in segment:
                segment['coordinates'] = [[0.2, 0.2], [0.8, 0.2], [0.8, 0.8], [0.2, 0.8]]
            if 'area_sqft' not in segment:
                segment['area_sqft'] = 800
            if 'suitability_score' not in segment:
                segment['suitability_score'] = 75
        
        # Validate solar zones
        for zone in validated_result['solar_zones']:
            if 'coordinates' not in zone:
                zone['coordinates'] = [[0.3, 0.3], [0.7, 0.3], [0.7, 0.7], [0.3, 0.7]]
            if 'color' not in zone:
                zone['color'] = self._get_zone_color(zone.get('zone_type', 'medium_suitability'))
        
        # Ensure system recommendations have defaults
        if not validated_result['system_recommendations']:
            validated_result['system_recommendations'] = {
                'total_system_size_kw': 6.0,
                'annual_production_kwh': 7200,
                'monthly_variation': [0.6, 0.7, 0.9, 1.1, 1.2, 1.3, 1.2, 1.1, 1.0, 0.8, 0.6, 0.5],
                'confidence_score': 75
            }
        
        return validated_result
    
    def _get_zone_color(self, zone_type: str) -> str:
        """Get appropriate color for solar zone type"""
        
        color_map = {
            'high_suitability': '#00AA00',    # Green
            'medium_suitability': '#FFAA00',  # Orange
            'low_suitability': '#FF6600',     # Red-orange
            'unsuitable': '#FF0000'           # Red
        }
        
        return color_map.get(zone_type, '#FFAA00')
