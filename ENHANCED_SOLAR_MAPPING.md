# Enhanced Solar Panel Mapping with GeoTiff Integration

## Overview

This implementation combines the logic from both provided code snippets to create a comprehensive solar panel mapping system that properly integrates satellite imagery with Google Solar API data, similar to Google's Project Sunroof.

## Key Features

### 1. Enhanced Solar Panel Visualization
- **Energy-based color coding**: Each solar panel is colored based on its yearly energy production potential
- **Accurate positioning**: Panels are positioned using real latitude/longitude coordinates from Google Solar API
- **Proper orientation**: Panels are rotated based on roof segment azimuth and orientation (portrait/landscape)
- **Realistic dimensions**: Panel sizes are calculated from actual meter measurements

### 2. GeoTiff-Style Processing
- **Heatmap overlay**: Solar flux data is overlaid on satellite imagery with transparency
- **Multi-layer visualization**: Combines satellite imagery, solar panels, and heatmap data
- **Pixel-perfect mapping**: Coordinates are converted to pixel positions for accurate placement

### 3. Composite Visualization
- **Layered approach**: Base satellite image + solar panels + heatmap overlay
- **Transparency blending**: Multiple layers are composited with appropriate alpha values
- **High-quality output**: Enhanced images maintain resolution and clarity

## Implementation Details

### Core Components

#### 1. Enhanced Image Processor (`utils/image_processor.py`)

**New Methods Added:**

```python
def _draw_enhanced_solar_panels(self, draw, width, height, solar_data, solar_datalayers):
    """Enhanced solar panel mapping using Google Solar API data with GeoTiff-style visualization"""
```

**Key Features:**
- Uses actual solar panel data from Google Solar API
- Applies energy-based color palette (green = high energy, red = low energy)
- Calculates panel positions using lat/lng coordinates
- Applies proper rotation based on roof orientation and azimuth

```python
def render_solar_heatmap_overlay(self, satellite_image_path, solar_datalayers):
    """Render solar heatmap overlay on satellite image using GeoTiff-style processing"""
```

**Key Features:**
- Downloads solar flux data from Google Solar API
- Resizes heatmap to match satellite image dimensions
- Applies transparency for proper blending
- Creates composite image with both satellite and heatmap data

```python
def create_composite_solar_visualization(self, satellite_path, analysis_data):
    """Create comprehensive solar visualization combining satellite, panels, and heatmap data"""
```

**Key Features:**
- Orchestrates the complete visualization pipeline
- Combines enhanced solar panels with heatmap overlays
- Provides fallback options when data is unavailable

#### 2. Color Palette System

**Energy-Based Colors:**
```python
[14, 249, 64],   # High energy - bright green
[32, 191, 85],   # Good energy - green  
[68, 154, 108],  # Medium energy - yellow-green
[109, 117, 131], # Low energy - gray-green
[154, 79, 154],  # Very low energy - purple
[199, 42, 177],  # Poor energy - magenta
[244, 4, 200]    # No energy - red
```

**Normalization Function:**
- Maps energy values (kWh/year) to color indices
- Handles edge cases (min = max energy)
- Provides smooth color transitions

#### 3. Coordinate Conversion

**Lat/Lng to Pixel Mapping:**
- Converts geographic coordinates to image pixel positions
- Accounts for image center point and scale
- Uses approximate meters-per-pixel calculation
- Handles coordinate system differences (Y-axis inversion)

**Panel Point Calculation:**
- Calculates corner points for each solar panel
- Applies rotation based on orientation (portrait/landscape)
- Incorporates roof azimuth for realistic positioning
- Creates proper polygon shapes for drawing

### Integration with Existing System

#### 1. Solar Visualization Component (`components/solar_visualization.py`)

**Updated to use enhanced mapping:**
```python
# Create comprehensive solar visualization with enhanced mapping
composite_path = image_processor.create_composite_solar_visualization(
    satellite_data['image_path'], 
    analysis_data
)
```

#### 2. Data Flow Integration

**Input Data Sources:**
- `satellite_imagery`: High-resolution satellite images
- `solar_data`: Google Solar API building insights with panel data
- `solar_datalayers`: Google Solar API data layers (flux, mask, DSM)
- `analysis_data`: Combined analysis results

**Processing Pipeline:**
1. Load base satellite image
2. Extract solar panel data (positions, energy, orientation)
3. Apply energy-based color mapping
4. Draw panels with proper positioning and rotation
5. Overlay solar flux heatmap data
6. Composite all layers with transparency
7. Save enhanced visualization

## Usage Examples

### Basic Enhanced Mapping
```python
from utils.image_processor import ImageProcessor

image_processor = ImageProcessor()

# Create composite visualization
enhanced_path = image_processor.create_composite_solar_visualization(
    satellite_image_path="path/to/satellite.png",
    analysis_data={
        'solar_data': solar_api_results,
        'solar_datalayers': heatmap_data,
        'satellite_imagery': satellite_info
    }
)
```

### Individual Components
```python
# Just enhanced solar panels
overlay_path = image_processor.create_solar_overlay(
    satellite_path, analysis_data
)

# Just heatmap overlay
heatmap_path = image_processor.render_solar_heatmap_overlay(
    satellite_path, solar_datalayers
)
```

## Testing

Run the comprehensive test suite:
```bash
python test_enhanced_solar_mapping.py
```

**Test Coverage:**
- Enhanced solar panel mapping functionality
- Color palette and energy normalization
- Coordinate conversion accuracy
- Heatmap overlay integration
- Composite visualization pipeline
- Error handling and fallbacks

## Error Handling

**Robust Fallbacks:**
- Falls back to basic panel grid if enhanced mapping fails
- Uses original satellite image if heatmap overlay fails
- Handles missing or invalid API data gracefully
- Provides informative error messages

**Data Validation:**
- Validates coordinate ranges and image dimensions
- Checks for valid URLs and image data
- Handles network timeouts and API errors
- Ensures proper image format compatibility

## Performance Considerations

**Optimizations:**
- Efficient coordinate conversion algorithms
- Minimal memory usage for large images
- Cached color palette calculations
- Optimized image compositing operations

**Scalability:**
- Handles varying numbers of solar panels (1-100+)
- Supports different image resolutions
- Adapts to different geographic scales
- Maintains performance with multiple data layers

## Future Enhancements

**Potential Improvements:**
- More accurate geographic projections
- Real-time solar irradiance data integration
- 3D visualization capabilities
- Interactive panel selection and analysis
- Time-based solar simulation
- Weather pattern integration

This enhanced implementation provides a production-ready solar panel mapping system that accurately visualizes solar potential using real Google Solar API data, similar to the quality and accuracy of Google's Project Sunroof.
