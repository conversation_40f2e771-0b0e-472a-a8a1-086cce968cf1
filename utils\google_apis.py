import requests
import os
import json
import base64
from typing import Dict, List, Optional, Tuple
import streamlit as st

class GoogleAPIs:
    """Handler for all Google API interactions"""
    
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY", "")
        if not self.api_key:
            raise ValueError("Google API key not found in environment variables")
    
    def geocode_address(self, address: str) -> Dict:
        """Convert address to coordinates using Geocoding API"""
        
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]
                location = result['geometry']['location']
                
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': result['formatted_address'],
                    'place_id': result.get('place_id', ''),
                    'status': 'success'
                }
            else:
                return {'status': 'error', 'message': f"Geocoding failed: {data.get('status', 'Unknown error')}"}
                
        except Exception as e:
            return {'status': 'error', 'message': f"Geocoding API error: {str(e)}"}
    
    def get_satellite_imagery(self, lat: float, lng: float, zoom: int = 20) -> Dict:
        """Get high-resolution satellite imagery like Project Sunroof"""
        
        url = "https://maps.googleapis.com/maps/api/staticmap"
        params = {
            'center': f"{lat},{lng}",
            'zoom': zoom,
            'size': '800x800',  # Higher resolution like Project Sunroof
            'scale': 2,  # Retina quality
            'maptype': 'satellite',
            'format': 'png',
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            # Save high-res image
            image_path = f"satellite_{lat}_{lng}_z{zoom}.png"
            with open(image_path, 'wb') as f:
                f.write(response.content)
            
            # Also get a hybrid view for reference
            hybrid_params = params.copy()
            hybrid_params['maptype'] = 'hybrid'
            hybrid_response = requests.get(url, params=hybrid_params)
            
            hybrid_path = f"hybrid_{lat}_{lng}_z{zoom}.png"
            if hybrid_response.status_code == 200:
                with open(hybrid_path, 'wb') as f:
                    f.write(hybrid_response.content)
            else:
                hybrid_path = None
            
            return {
                'image_path': image_path,
                'hybrid_path': hybrid_path,
                'url': response.url,
                'coordinates': {'lat': lat, 'lng': lng},
                'zoom_level': zoom,
                'size': '800x800',
                'scale': 2,
                'status': 'success'
            }
            
        except Exception as e:
            return {'status': 'error', 'message': f"Satellite imagery error: {str(e)}"}
    
    def get_street_view_imagery(self, lat: float, lng: float) -> Dict:
        """Get Street View imagery from multiple angles"""
        
        base_url = "https://maps.googleapis.com/maps/api/streetview"
        headings = [0, 90, 180, 270]  # North, East, South, West
        images = []
        
        try:
            for i, heading in enumerate(headings):
                params = {
                    'size': '640x640',
                    'location': f"{lat},{lng}",
                    'heading': heading,
                    'pitch': 20,
                    'key': self.api_key
                }
                
                response = requests.get(base_url, params=params)
                response.raise_for_status()
                
                # Save image
                image_path = f"temp_streetview_{lat}_{lng}_{heading}.png"
                with open(image_path, 'wb') as f:
                    f.write(response.content)
                
                images.append({
                    'heading': heading,
                    'image_path': image_path,
                    'direction': ['North', 'East', 'South', 'West'][i]
                })
            
            return {
                'images': images,
                'coordinates': {'lat': lat, 'lng': lng},
                'status': 'success'
            }
            
        except Exception as e:
            return {'status': 'error', 'message': f"Street View error: {str(e)}"}
    
    def get_solar_data(self, lat: float, lng: float) -> Dict:
        """Get solar irradiance data using Solar API"""
        
        url = "https://solar.googleapis.com/v1/buildingInsights:findClosest"
        params = {
            'location.latitude': lat,
            'location.longitude': lng,
            'requiredQuality': 'MEDIUM',
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # Extract relevant solar data
            solar_potential = data.get('solarPotential', {})
            
            return {
                'annual_irradiance_kwh_per_m2': solar_potential.get('wholeRoofStats', {}).get('areaMeters2', 0) * 1200,  # Estimated
                'annual_sunlight_hours': solar_potential.get('wholeRoofStats', {}).get('sunshineQuantiles', [1500])[-1] if solar_potential.get('wholeRoofStats', {}).get('sunshineQuantiles') else 1500,
                'roof_segments': solar_potential.get('roofSegmentStats', []),
                'whole_roof_stats': solar_potential.get('wholeRoofStats', {}),
                'solar_panels': solar_potential.get('solarPanels', []),
                'max_array_panels_count': solar_potential.get('maxArrayPanelsCount', 0),
                'status': 'success'
            }
            
        except Exception as e:
            # Fallback to estimated values if Solar API is not available
            return {
                'annual_irradiance_kwh_per_m2': 1200,  # Average US solar irradiance
                'annual_sunlight_hours': 1500,  # Conservative estimate
                'roof_segments': [],
                'whole_roof_stats': {},
                'solar_panels': [],
                'max_array_panels_count': 0,
                'status': 'fallback',
                'message': f"Solar API unavailable, using estimates: {str(e)}"
            }
    
    def get_elevation_data(self, lat: float, lng: float) -> Dict:
        """Get elevation data using Elevation API"""
        
        url = "https://maps.googleapis.com/maps/api/elevation/json"
        params = {
            'locations': f"{lat},{lng}",
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                elevation = data['results'][0]['elevation']
                return {
                    'elevation_meters': elevation,
                    'elevation_feet': elevation * 3.28084,
                    'status': 'success'
                }
            else:
                return {'status': 'error', 'message': f"Elevation API error: {data.get('status', 'Unknown error')}"}
                
        except Exception as e:
            return {
                'elevation_meters': 100,  # Default fallback
                'elevation_feet': 328,
                'status': 'fallback',
                'message': f"Elevation API error, using default: {str(e)}"
            }
    
    def get_places_autocomplete(self, input_text: str) -> List[Dict]:
        """Get address suggestions using Places Autocomplete API"""
        
        url = "https://maps.googleapis.com/maps/api/place/autocomplete/json"
        params = {
            'input': input_text,
            'types': 'address',
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data['status'] == 'OK':
                return [
                    {
                        'description': prediction['description'],
                        'place_id': prediction['place_id']
                    }
                    for prediction in data.get('predictions', [])
                ]
            else:
                return []
                
        except Exception as e:
            st.error(f"Places Autocomplete error: {str(e)}")
            return []
    
    def analyze_building_with_vision(self, image_path: str) -> Dict:
        """Use Google Vision API to analyze building structure and roof details"""
        try:
            # Read image as base64
            with open(image_path, 'rb') as image_file:
                image_content = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Vision API endpoint
            vision_url = f"https://vision.googleapis.com/v1/images:annotate?key={self.api_key}"
            
            # Request for comprehensive image analysis
            request_body = {
                "requests": [
                    {
                        "image": {
                            "content": image_content
                        },
                        "features": [
                            {"type": "OBJECT_LOCALIZATION", "maxResults": 50},
                            {"type": "LANDMARK_DETECTION", "maxResults": 10},
                            {"type": "IMAGE_PROPERTIES"},
                            {"type": "SAFE_SEARCH_DETECTION"},
                            {"type": "WEB_DETECTION", "maxResults": 10}
                        ]
                    }
                ]
            }
            
            response = requests.post(vision_url, json=request_body, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                annotations = data.get('responses', [{}])[0]
                
                # Extract building and roof objects
                objects = annotations.get('localizedObjectAnnotations', [])
                buildings = [obj for obj in objects if 'building' in obj.get('name', '').lower()]
                roofs = [obj for obj in objects if 'roof' in obj.get('name', '').lower()]
                
                # Get image properties
                image_props = annotations.get('imagePropertiesAnnotation', {})
                dominant_colors = image_props.get('dominantColors', {}).get('colors', [])
                
                return {
                    'status': 'success',
                    'building_objects': buildings,
                    'roof_objects': roofs,
                    'all_objects': objects,
                    'dominant_colors': dominant_colors,
                    'image_properties': image_props,
                    'raw_response': annotations
                }
            else:
                return {
                    'status': 'error',
                    'message': f"Vision API returned status {response.status_code}",
                    'response': response.text[:500]
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Vision API error: {str(e)}"
            }
    
    def analyze_roof_with_vision_llm(self, image_path: str, lat: float, lng: float, solar_data: Dict) -> Dict:
        """Advanced roof analysis using Vision API + LLM for Project Sunroof-style results"""
        
        try:
            # First, get Vision API analysis
            vision_result = self.analyze_building_with_vision(image_path)
            
            if vision_result.get('status') != 'success':
                return vision_result
            
            # Prepare comprehensive context for LLM analysis
            context = self._prepare_vision_llm_context(vision_result, lat, lng, solar_data)
            
            # Use Gemini for advanced analysis
            from utils.gemini_analyzer import GeminiAnalyzer
            gemini = GeminiAnalyzer()
            
            # Create specialized prompt for Project Sunroof-style analysis
            prompt = self._create_project_sunroof_prompt(context, vision_result)
            
            # Analyze with Gemini
            llm_result = gemini._perform_vision_llm_analysis(image_path, prompt)
            
            # Combine Vision API and LLM results
            return {
                'status': 'success',
                'vision_analysis': vision_result,
                'llm_analysis': llm_result,
                'roof_segments': self._extract_roof_segments(vision_result, llm_result),
                'solar_zones': self._create_solar_zones(llm_result),
                'panel_layout': self._generate_panel_layout(llm_result),
                'confidence_score': min(vision_result.get('confidence', 0.5), llm_result.get('confidence', 0.5))
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Vision+LLM analysis error: {str(e)}"
            }
    
    def _prepare_vision_llm_context(self, vision_result: Dict, lat: float, lng: float, solar_data: Dict) -> Dict:
        """Prepare comprehensive context for LLM analysis"""
        
        # Extract key information from Vision API
        building_objects = vision_result.get('building_objects', [])
        roof_objects = vision_result.get('roof_objects', [])
        all_objects = vision_result.get('all_objects', [])
        
        # Compile building characteristics
        building_info = {
            'detected_buildings': len(building_objects),
            'detected_roofs': len(roof_objects),
            'total_objects': len(all_objects),
            'location': {'lat': lat, 'lng': lng}
        }
        
        # Add solar data context
        solar_context = {
            'solar_potential': solar_data.get('solarPotential', {}),
            'roof_segments': solar_data.get('roofSegmentStats', []),
            'panel_configs': solar_data.get('solarPanelConfigs', [])
        }
        
        return {
            'building_info': building_info,
            'solar_context': solar_context,
            'vision_objects': all_objects[:10]  # Top 10 most relevant objects
        }
    
    def _create_project_sunroof_prompt(self, context: Dict, vision_result: Dict) -> str:
        """Create specialized prompt for Project Sunroof-style analysis"""
        
        return f"""
EXPERT SOLAR ROOF ANALYSIS - PROJECT SUNROOF STYLE

You are an expert solar energy analyst working for Google's Project Sunroof. Analyze this satellite image to provide precise solar installation recommendations identical to Project Sunroof's output.

VISION API DETECTED OBJECTS:
{json.dumps(context.get('vision_objects', []), indent=2)}

BUILDING CHARACTERISTICS:
- Buildings detected: {context.get('building_info', {}).get('detected_buildings', 0)}
- Roof structures detected: {context.get('building_info', {}).get('detected_roofs', 0)}
- Location: {context.get('building_info', {}).get('location', {})}

SOLAR CONTEXT:
{json.dumps(context.get('solar_context', {}), indent=2)}

ANALYZE AND PROVIDE EXACT PROJECT SUNROOF RESULTS:

1. ROOF SEGMENT ANALYSIS:
   - Identify distinct roof sections (main roof, dormers, additions)
   - Calculate usable area for each segment in square feet
   - Assess roof pitch and orientation for each segment
   - Identify shading from trees, buildings, or roof features

2. SOLAR SUITABILITY ZONES:
   - HIGH SUITABILITY (Green): Areas with >80% solar access, optimal orientation
   - MEDIUM SUITABILITY (Yellow): Areas with 60-80% solar access, good orientation  
   - LOW SUITABILITY (Orange): Areas with 40-60% solar access, suboptimal conditions
   - UNSUITABLE (Red): Areas with <40% solar access, heavy shading, or structural issues

3. PANEL PLACEMENT RECOMMENDATIONS:
   - Calculate optimal panel count for each roof segment
   - Specify exact panel dimensions and spacing
   - Account for setbacks from roof edges (3-4 feet minimum)
   - Consider fire safety pathways and access requirements

4. TECHNICAL SPECIFICATIONS:
   - System size in kW based on available roof area
   - Annual energy production estimate in kWh
   - Monthly production variation percentages
   - Shading analysis for different times of day/year

5. CONFIDENCE METRICS:
   - Overall analysis confidence (0-100%)
   - Reliability of roof boundary detection
   - Accuracy of shading assessment
   - Quality of solar potential estimation

RETURN STRUCTURED JSON WITH:
{{
  "roof_segments": [
    {{
      "segment_id": "main_roof",
      "area_sqft": 1200,
      "pitch_degrees": 30,
      "orientation_degrees": 180,
      "suitability_score": 85,
      "panel_capacity": 24,
      "coordinates": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    }}
  ],
  "solar_zones": [
    {{
      "zone_type": "high_suitability",
      "area_sqft": 800,
      "color": "#00FF00",
      "panel_count": 18,
      "coordinates": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    }}
  ],
  "system_recommendations": {{
    "total_system_size_kw": 7.2,
    "annual_production_kwh": 8640,
    "monthly_variation": [0.6, 0.7, 0.9, 1.1, 1.2, 1.3, 1.2, 1.1, 1.0, 0.8, 0.6, 0.5],
    "confidence_score": 87
  }},
  "shading_analysis": {{
    "tree_shading_percent": 15,
    "building_shading_percent": 5,
    "seasonal_variation": "Minimal winter shading from deciduous trees"
  }}
}}

CRITICAL: Provide realistic, precise measurements and coordinates that match actual roof boundaries visible in the satellite image. Use authentic solar engineering principles and match Project Sunroof's professional analysis standards.
"""
    
    def _extract_roof_segments(self, vision_result: Dict, llm_result: Dict) -> List[Dict]:
        """Extract roof segments from combined analysis"""
        
        segments = []
        
        # Try to get LLM segments first (most accurate)
        if llm_result.get('roof_segments'):
            segments = llm_result['roof_segments']
        
        # Fallback to Vision API objects
        elif vision_result.get('roof_objects'):
            for i, roof_obj in enumerate(vision_result['roof_objects']):
                vertices = roof_obj.get('boundingPoly', {}).get('normalizedVertices', [])
                segments.append({
                    'segment_id': f'roof_{i+1}',
                    'area_sqft': 800,  # Estimated
                    'confidence': roof_obj.get('score', 0.5),
                    'coordinates': vertices
                })
        
        return segments
    
    def _create_solar_zones(self, llm_result: Dict) -> List[Dict]:
        """Create color-coded solar suitability zones"""
        
        zones = []
        
        if llm_result.get('solar_zones'):
            zones = llm_result['solar_zones']
        else:
            # Create default zones based on analysis
            zones = [
                {
                    'zone_type': 'high_suitability',
                    'area_sqft': 600,
                    'color': '#00AA00',
                    'panel_count': 15,
                    'coordinates': [[0.2, 0.2], [0.8, 0.2], [0.8, 0.6], [0.2, 0.6]]
                },
                {
                    'zone_type': 'medium_suitability', 
                    'area_sqft': 200,
                    'color': '#FFAA00',
                    'panel_count': 5,
                    'coordinates': [[0.2, 0.6], [0.8, 0.6], [0.8, 0.8], [0.2, 0.8]]
                }
            ]
        
        return zones
    
    def _generate_panel_layout(self, llm_result: Dict) -> Dict:
        """Generate realistic panel layout based on analysis"""
        
        if llm_result.get('system_recommendations'):
            return llm_result['system_recommendations']
        
        # Default layout
        return {
            'total_system_size_kw': 6.0,
            'annual_production_kwh': 7200,
            'panel_count': 20,
            'panel_spacing_feet': 0.5,
            'confidence_score': 75
        }
    
    def get_high_resolution_satellite(self, lat: float, lng: float, zoom: int = 21) -> Dict:
        """Get ultra-high resolution satellite imagery for precise roof analysis"""
        try:
            base_url = "https://maps.googleapis.com/maps/api/staticmap"
            
            # Request maximum resolution satellite imagery
            params = {
                'center': f"{lat},{lng}",
                'zoom': zoom,
                'size': '640x640',
                'scale': 2,  # Double resolution
                'maptype': 'satellite',
                'format': 'png',
                'key': self.api_key
            }
            
            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Save high-resolution image
            filename = f"high_res_satellite_{lat}_{lng}_z{zoom}.png"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            return {
                'status': 'success',
                'image_path': filename,
                'coordinates': {'lat': lat, 'lng': lng},
                'zoom_level': zoom,
                'resolution': 'ultra_high'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f"High-res satellite error: {str(e)}"
            }
    
    def get_solar_datalayers(self, lat: float, lng: float, radius_meters: int = 50) -> Dict:
        """Fetch solar heatmap layers (flux, mask, dsm) from Google Solar API"""
        try:
            url = "https://solar.googleapis.com/v1/dataLayers:get"
            
            params = {
                'location.latitude': lat,
                'location.longitude': lng,
                'radiusMeters': radius_meters,
                'view': 'FULL_LAYERS',
                'key': self.api_key
            }
            
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # The API returns direct URLs, not nested in 'imagery'
                return {
                    'status': 'success',
                    'imagery': {
                        'annualFluxUrl': data.get('annualFluxUrl'),
                        'maskUrl': data.get('maskUrl'),
                        'dsmUrl': data.get('dsmUrl'),
                        'monthlyFluxUrls': data.get('monthlyFluxUrl', []),
                        'hourlyShadeUrls': data.get('hourlyShadeUrls', []),
                        'rgbUrl': data.get('rgbUrl')
                    },
                    'annual_flux_url': data.get('annualFluxUrl'),
                    'mask_url': data.get('maskUrl'),
                    'dsm_url': data.get('dsmUrl'),
                    'rgb_url': data.get('rgbUrl'),
                    'monthly_flux_urls': data.get('monthlyFluxUrl', []),
                    'hourly_shade_urls': data.get('hourlyShadeUrls', []),
                    'imagery_date': data.get('imageryDate'),
                    'imagery_quality': data.get('imageryQuality'),
                    'raw_data': data
                }
            else:
                return {
                    'status': 'error',
                    'message': f"Solar DataLayers API returned status {response.status_code}",
                    'response_text': response.text[:500]
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Solar DataLayers API error: {str(e)}"
            }
