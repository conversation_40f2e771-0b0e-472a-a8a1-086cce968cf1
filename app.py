import streamlit as st
import os
from components.address_input import render_address_input
from components.solar_visualization import render_solar_visualization
from components.results_display import render_results_display, render_key_metrics_header
from utils.google_apis import GoogleAPIs
from utils.gemini_analyzer import GeminiAnalyzer
from utils.pdf_generator import PDFGenerator
import json

# Configure page
st.set_page_config(
    page_title="Solar Roof Analyzer",
    page_icon="☀️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analysis_complete' not in st.session_state:
    st.session_state.analysis_complete = False
if 'analysis_data' not in st.session_state:
    st.session_state.analysis_data = {}
if 'current_address' not in st.session_state:
    st.session_state.current_address = ""

def main():
    # Header
    st.title("☀️ Solar Roof Analyzer")
    st.markdown("**Comprehensive solar potential assessment powered by Google APIs and Gemini AI**")
    
    # Check API keys
    with st.sidebar:
        st.header("🔧 Configuration")
        
        # API Status indicators
        google_api_key = os.getenv("GOOGLE_API_KEY", "")
        gemini_api_key = os.getenv("GEMINI_API_KEY", "")
        
        if google_api_key:
            st.success("✅ Google API Key configured")
        else:
            st.error("❌ Google API Key missing")
            st.stop()
            
        if gemini_api_key:
            st.success("✅ Gemini API Key configured")
        else:
            st.error("❌ Gemini API Key missing")
            st.stop()
        
        # Analysis options
        st.subheader("Analysis Options")
        include_street_view = st.checkbox("Include Street View Analysis", value=True)
        generate_pdf = st.checkbox("Generate PDF Report", value=True)
        zoom_level = st.slider("Satellite Image Zoom Level", 18, 22, 20)
        
        # Technical parameters
        st.subheader("Technical Parameters")
        panel_efficiency = st.slider("Panel Efficiency (%)", 15, 25, 20)
        system_losses = st.slider("System Losses (%)", 10, 20, 14)
        electricity_rate = st.number_input("Electricity Rate ($/kWh)", 0.05, 0.50, 0.12, 0.01)
    
    # Main content area - full width layout like Project Sunroof
    st.header("🏠 Property Solar Analysis")
    
    # Address input at the top
    address_data = render_address_input()
    
    if address_data and address_data.get('address'):
        current_address = address_data['address']
        
        # Check if we need to run new analysis
        if current_address != st.session_state.current_address:
            st.session_state.current_address = current_address
            st.session_state.analysis_complete = False
        
        # Analysis button - prominent like Project Sunroof
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔍 Analyze Solar Potential", type="primary", use_container_width=True, key="analyze_btn"):
                run_analysis(
                    address_data, 
                    include_street_view, 
                    zoom_level,
                    panel_efficiency,
                    system_losses,
                    electricity_rate
                )
        
        # Show results in full width
        if st.session_state.analysis_complete and st.session_state.analysis_data:
            st.divider()
            
            # Key metrics at top like Project Sunroof
            render_key_metrics_header(st.session_state.analysis_data)
            
            # Visualization takes priority
            st.header("🗺️ Solar Analysis Map")
            render_solar_visualization(st.session_state.analysis_data)
            
            # Detailed results below
            st.header("📊 Detailed Analysis")
            render_results_display(st.session_state.analysis_data)
            
            # PDF generation
            if generate_pdf:
                col1, col2, col3 = st.columns([1, 2, 1])
                with col2:
                    if st.button("📄 Generate PDF Report", use_container_width=True):
                        generate_pdf_report(st.session_state.analysis_data)
    else:
        # Project Sunroof style landing
        st.markdown("""
        ### How it works:
        1. **Enter your address** - We'll find your property using satellite imagery
        2. **AI analysis** - Our system analyzes your roof for solar potential
        3. **Get recommendations** - Receive detailed solar installation insights
        """)
        st.info("👆 Enter your property address above to begin solar analysis")
    
    # Visualization section
    if st.session_state.analysis_complete and st.session_state.analysis_data:
        st.header("🗺️ Solar Visualization")
        render_solar_visualization(st.session_state.analysis_data)

def run_analysis(address_data, include_street_view, zoom_level, panel_efficiency, system_losses, electricity_rate):
    """Run comprehensive solar analysis"""
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Initialize APIs
        google_apis = GoogleAPIs()
        gemini_analyzer = GeminiAnalyzer()
        
        analysis_data = {
            'address': address_data['address'],
            'coordinates': address_data.get('coordinates', {}),
            'parameters': {
                'panel_efficiency': panel_efficiency,
                'system_losses': system_losses,
                'electricity_rate': electricity_rate,
                'zoom_level': zoom_level
            }
        }
        
        # Step 1: Get satellite imagery
        status_text.text("📡 Retrieving high-resolution satellite imagery...")
        progress_bar.progress(10)
        
        satellite_data = google_apis.get_satellite_imagery(
            address_data['coordinates']['lat'],
            address_data['coordinates']['lng'],
            zoom_level
        )
        analysis_data['satellite_imagery'] = satellite_data
        
        # Step 2: Get Street View data (optional)
        if include_street_view:
            status_text.text("📷 Capturing Street View imagery...")
            progress_bar.progress(25)
            
            street_view_data = google_apis.get_street_view_imagery(
                address_data['coordinates']['lat'],
                address_data['coordinates']['lng']
            )
            analysis_data['street_view'] = street_view_data
        
        # Step 3: Get Solar API data
        status_text.text("☀️ Retrieving solar irradiance data...")
        progress_bar.progress(40)
        
        solar_data = google_apis.get_solar_data(
            address_data['coordinates']['lat'],
            address_data['coordinates']['lng']
        )
        analysis_data['solar_data'] = solar_data
        
        # Step 4: Get elevation data
        status_text.text("🏔️ Getting elevation information...")
        progress_bar.progress(55)
        
        elevation_data = google_apis.get_elevation_data(
            address_data['coordinates']['lat'],
            address_data['coordinates']['lng']
        )
        analysis_data['elevation'] = elevation_data
        
        # Step 5: Solar DataLayers (heatmap data)
        status_text.text("🗺️ Fetching solar heatmap layers...")
        progress_bar.progress(55)
        
        solar_datalayers = google_apis.get_solar_datalayers(
            address_data['coordinates']['lat'],
            address_data['coordinates']['lng']
        )
        analysis_data['solar_datalayers'] = solar_datalayers
        
        # Step 6: Vision API analysis for precise building detection
        status_text.text("👁️ Analyzing building structure with Vision API...")
        progress_bar.progress(60)
        
        vision_analysis = {}
        if satellite_data.get('image_path'):
            vision_analysis = google_apis.analyze_building_with_vision(satellite_data['image_path'])
            analysis_data['vision_analysis'] = vision_analysis
        
        # Step 7: Advanced Vision+LLM analysis for Project Sunroof-style results
        status_text.text("🎯 Performing advanced Vision+LLM analysis...")
        progress_bar.progress(70)
        
        vision_llm_analysis = {}
        if satellite_data.get('image_path'):
            vision_llm_analysis = google_apis.analyze_roof_with_vision_llm(
                satellite_data['image_path'],
                address_data['coordinates']['lat'],
                address_data['coordinates']['lng'],
                solar_data
            )
            analysis_data['vision_llm_analysis'] = vision_llm_analysis
        
        # Step 8: AI-powered roof analysis with Gemini (fallback)
        status_text.text("🤖 Running AI-powered roof analysis...")
        progress_bar.progress(75)
        
        if satellite_data.get('image_path'):
            roof_analysis = gemini_analyzer.analyze_roof_image(
                satellite_data['image_path'],
                solar_data,
                elevation_data
            )
            analysis_data['roof_analysis'] = roof_analysis
        
        # Step 6: Calculate system estimates
        status_text.text("⚡ Calculating system estimates...")
        progress_bar.progress(85)
        
        system_estimates = calculate_system_estimates(
            analysis_data,
            panel_efficiency,
            system_losses,
            electricity_rate
        )
        analysis_data['system_estimates'] = system_estimates
        
        # Step 7: Finalize analysis
        status_text.text("✅ Analysis complete!")
        progress_bar.progress(100)
        
        # Store results
        st.session_state.analysis_data = analysis_data
        st.session_state.analysis_complete = True
        
        st.success("✅ Solar analysis completed successfully!")
        st.rerun()
        
    except Exception as e:
        st.error(f"❌ Analysis failed: {str(e)}")
        progress_bar.empty()
        status_text.empty()

def calculate_system_estimates(analysis_data, panel_efficiency, system_losses, electricity_rate):
    """Calculate solar system estimates and ROI"""
    
    solar_data = analysis_data.get('solar_data', {})
    roof_analysis = analysis_data.get('roof_analysis', {})
    
    # Extract key metrics
    usable_roof_area = roof_analysis.get('usable_area_sqft', 0)
    annual_sunlight_hours = solar_data.get('annual_sunlight_hours', 1500)
    solar_irradiance = solar_data.get('annual_irradiance_kwh_per_m2', 1200)
    
    # Panel specifications (typical residential solar panel)
    panel_wattage = 400  # Watts per panel
    panel_area_sqft = 22  # Square feet per panel
    
    # Calculate number of panels
    max_panels = int(usable_roof_area / panel_area_sqft)
    
    # System capacity
    system_capacity_kw = (max_panels * panel_wattage) / 1000
    
    # Annual energy generation
    capacity_factor = 0.15  # Typical for residential solar
    annual_generation_kwh = system_capacity_kw * 8760 * capacity_factor * (1 - system_losses/100)
    
    # Financial calculations
    cost_per_watt = 3.00  # Typical installed cost
    system_cost = system_capacity_kw * 1000 * cost_per_watt
    
    # Federal tax credit (30% as of current policy)
    federal_tax_credit = system_cost * 0.30
    net_system_cost = system_cost - federal_tax_credit
    
    # Annual savings
    annual_savings = annual_generation_kwh * electricity_rate
    
    # Payback period
    payback_period = net_system_cost / annual_savings if annual_savings > 0 else 0
    
    # 20-year savings
    savings_20_years = (annual_savings * 20) - net_system_cost
    
    return {
        'usable_roof_area': usable_roof_area,
        'max_panels': max_panels,
        'system_capacity_kw': system_capacity_kw,
        'annual_generation_kwh': annual_generation_kwh,
        'system_cost': system_cost,
        'federal_tax_credit': federal_tax_credit,
        'net_system_cost': net_system_cost,
        'annual_savings': annual_savings,
        'payback_period': payback_period,
        'savings_20_years': savings_20_years,
        'annual_sunlight_hours': annual_sunlight_hours
    }

def generate_pdf_report(analysis_data):
    """Generate and offer PDF report for download"""
    
    try:
        pdf_generator = PDFGenerator()
        pdf_buffer = pdf_generator.generate_report(analysis_data)
        
        st.download_button(
            label="📥 Download PDF Report",
            data=pdf_buffer.getvalue(),
            file_name=f"solar_analysis_{analysis_data['address'].replace(' ', '_')}.pdf",
            mime="application/pdf",
            use_container_width=True
        )
        
        st.success("✅ PDF report generated successfully!")
        
    except Exception as e:
        st.error(f"❌ Failed to generate PDF: {str(e)}")

if __name__ == "__main__":
    main()
