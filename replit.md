# Solar Roof Analyzer

## Overview

The Solar Roof Analyzer is a comprehensive Streamlit web application that provides AI-powered solar potential assessment for residential properties. The application integrates Google APIs for geospatial data, Gemini AI for intelligent roof analysis, and generates professional PDF reports. Users can input property addresses to receive detailed solar installation recommendations, energy production estimates, and technical feasibility assessments.

## System Architecture

### Frontend Architecture
- **Framework**: Streamlit web application with multi-page layout
- **Component Structure**: Modular component-based architecture with separate modules for:
  - Address input with autocomplete functionality
  - Interactive solar visualizations with satellite and street view imagery
  - Comprehensive results display with tabbed interface
- **State Management**: Streamlit session state for maintaining analysis data across user interactions

### Backend Architecture
- **Core Application**: Python-based with `app.py` as the main entry point
- **Utility Layer**: Specialized utility classes for external API integrations and data processing
- **API Integration Layer**: Centralized handlers for Google APIs and Gemini AI services
- **Image Processing**: Computer vision capabilities for satellite imagery analysis and solar overlay generation

### Data Processing Pipeline
1. Address geocoding and validation
2. Satellite imagery acquisition
3. AI-powered roof analysis using Gemini
4. Solar potential calculations and system sizing
5. Visualization generation with overlay mapping
6. Professional PDF report generation

## Key Components

### Core Application (`app.py`)
- Main Streamlit application entry point
- API key validation and configuration management
- Session state initialization and management
- Component orchestration and user flow control

### Address Input Component (`components/address_input.py`)
- Google Geocoding API integration for address validation
- Coordinate extraction and formatting
- Real-time address autocomplete functionality
- Error handling for invalid addresses

### Solar Visualization Component (`components/solar_visualization.py`)
- Multi-tab visualization interface
- Satellite imagery display with solar suitability overlays
- Street view integration for property context
- Interactive charts for solar metrics and energy production forecasting

### Results Display Component (`components/results_display.py`)
- Comprehensive analysis results presentation
- Tabbed interface for main analysis, technical data, and recommendations
- Key performance metrics display with visual indicators
- Responsive layout with metric cards and detailed breakdowns

### Google APIs Utility (`utils/google_apis.py`)
- Centralized Google API client management
- Geocoding API for address-to-coordinate conversion
- Static Maps API for high-resolution satellite imagery
- Places API integration for enhanced address validation
- Error handling and rate limiting considerations

### Gemini Analyzer (`utils/gemini_analyzer.py`)
- AI-powered roof analysis using Google's Gemini model
- Structured output using Pydantic models for data validation
- Image analysis for roof condition, orientation, and shading assessment
- Solar suitability scoring and panel placement recommendations
- Integration with satellite imagery and elevation data

### Image Processor (`utils/image_processor.py`)
- Computer vision processing for satellite imagery
- Solar suitability overlay generation with color-coded zones
- House boundary detection and visualization
- Panel placement visualization on roof surfaces
- Image enhancement and optimization for web display

### PDF Generator (`utils/pdf_generator.py`)
- Professional report generation using ReportLab
- Custom styling and branding for solar analysis reports
- Charts and visualizations integration
- Multi-page layout with structured content sections
- Export functionality for client deliverables

## Data Flow

1. **Address Input**: User enters property address → Geocoding API validates and returns coordinates
2. **Imagery Acquisition**: Coordinates used to fetch high-resolution satellite imagery via Google Static Maps API
3. **AI Analysis**: Satellite images analyzed by Gemini AI for roof characteristics, solar potential, and installation feasibility
4. **Data Processing**: Analysis results processed to generate system sizing, energy estimates, and financial projections
5. **Visualization**: Processed data rendered in interactive visualizations with solar suitability overlays
6. **Report Generation**: Comprehensive PDF report generated with analysis results and recommendations

## External Dependencies

### Google Cloud APIs
- **Geocoding API**: Address validation and coordinate conversion
- **Static Maps API**: High-resolution satellite imagery acquisition
- **Places API**: Enhanced address autocomplete functionality
- Authentication via `GOOGLE_API_KEY` environment variable

### Gemini AI
- **Gemini Pro Vision**: AI-powered image analysis and roof assessment
- Structured output generation for consistent data formatting
- Authentication via `GEMINI_API_KEY` environment variable

### Python Libraries
- **Streamlit**: Web application framework and UI components
- **Plotly**: Interactive charts and visualizations
- **PIL/OpenCV**: Image processing and computer vision
- **ReportLab**: PDF generation and document formatting
- **Pydantic**: Data validation and structured output
- **Requests**: HTTP client for API communications

## Deployment Strategy

### Environment Configuration
- Environment variables required: `GOOGLE_API_KEY`, `GEMINI_API_KEY`
- Streamlit configuration for wide layout and custom page settings
- Error handling for missing API credentials with user-friendly feedback

### Application Structure
- Modular component architecture supports easy maintenance and feature additions
- Separation of concerns between UI components and business logic
- Utility classes provide reusable functionality across components

### Performance Considerations
- Streamlit session state for efficient data persistence
- Image caching and optimization for faster load times
- API rate limiting awareness in Google Services integration

## Changelog

```
Changelog:
- July 01, 2025: Initial setup with comprehensive solar analyzer
- July 01, 2025: Enhanced UI to match Project Sunroof style
  - Improved satellite imagery display with higher resolution (800x800, scale 2)
  - Added hybrid map view option
  - Redesigned layout with prominent address search
  - Fixed UnboundLocalError in technical data display
  - Enhanced visualization with full-width layout like Google's Project Sunroof
  - Added real-time address validation and geocoding
- July 01, 2025: Comprehensive Google APIs integration for authentic Project Sunroof experience
  - Integrated Google Solar API for authentic solar potential data
  - Added Google Vision API for precise building and roof detection
  - Enhanced image processor with authentic solar zone visualization
  - Implemented realistic solar panel placement using actual roof segment data
  - Added fallback systems for areas without full API coverage
  - Created color-coded suitability zones using real sunshine quantile data
  - Fixed all chart rendering errors and improved application stability
- July 01, 2025: Google Solar API DataLayers integration for professional heatmap visualization
  - Added Solar DataLayers API for annual flux, roof mask, and DSM heatmaps
  - Implemented side-by-side comparison with satellite imagery like Google Solar demo
  - Created comprehensive heatmap display component with monthly/hourly data
  - Added technical layer interpretation guide for professional analysis
  - Integrated 189+ monthly flux layers and 12 hourly shade analysis patterns
  - Enhanced visualization grid for complete solar data comparison
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```