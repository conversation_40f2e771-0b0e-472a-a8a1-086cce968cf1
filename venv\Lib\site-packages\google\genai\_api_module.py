# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

"""Utilities for the API Modules of the Google Gen AI SDK."""

from typing import Optional
from . import _api_client


class BaseModule:

  def __init__(self, api_client_: _api_client.BaseApiClient):
    self._api_client = api_client_

  @property
  def vertexai(self) -> Optional[bool]:
    return self._api_client.vertexai
