#!/usr/bin/env python3
"""
Test script to verify the enhanced roof detection and solar visualization
"""

from utils.image_processor import ImageProcessor
import os

def test_roof_detection():
    """Test the enhanced roof detection functionality"""
    
    # Create a test analysis data structure
    test_analysis = {
        'roof_analysis': {
            'solar_suitability_score': 8,
            'suitability_zones': {
                'primary_zone': 'excellent_suitability',
                'coverage_percentage': 0.8
            }
        },
        'system_estimates': {
            'max_panels': 25,
            'system_capacity_kw': 10.0
        }
    }
    
    # Test if we have existing satellite images
    test_images = [f for f in os.listdir('.') if f.startswith('satellite_') and f.endswith('.png')]
    
    if test_images:
        print(f"Found {len(test_images)} satellite images to test")
        
        # Test with the first available image
        test_image = test_images[0]
        print(f"Testing with image: {test_image}")
        
        # Create image processor and test overlay
        processor = ImageProcessor()
        
        try:
            overlay_path = processor.create_solar_overlay(test_image, test_analysis)
            print(f"✅ Successfully created overlay: {overlay_path}")
            
            # Check if the file was created
            if os.path.exists(overlay_path):
                print(f"✅ Overlay file exists and is {os.path.getsize(overlay_path)} bytes")
                return True
            else:
                print("❌ Overlay file was not created")
                return False
                
        except Exception as e:
            print(f"❌ Error creating overlay: {e}")
            return False
    else:
        print("No satellite images found for testing")
        return False

if __name__ == "__main__":
    print("Testing Enhanced Roof Detection...")
    success = test_roof_detection()
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")