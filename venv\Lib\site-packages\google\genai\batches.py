# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import json
import logging
from typing import Any, Optional, Union
from urllib.parse import urlencode

from . import _api_module
from . import _common
from . import _extra_utils
from . import _transformers as t
from . import types
from ._api_client import BaseApiClient
from ._common import get_value_by_path as getv
from ._common import set_value_by_path as setv
from .pagers import AsyncPager, Pager

logger = logging.getLogger('google_genai.batches')


def _VideoMetadata_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['end_offset']) is not None:
    setv(to_object, ['endOffset'], getv(from_object, ['end_offset']))

  if getv(from_object, ['start_offset']) is not None:
    setv(to_object, ['startOffset'], getv(from_object, ['start_offset']))

  return to_object


def _Blob_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    raise ValueError('display_name parameter is not supported in Gemini API.')

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _FileData_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    raise ValueError('display_name parameter is not supported in Gemini API.')

  if getv(from_object, ['file_uri']) is not None:
    setv(to_object, ['fileUri'], getv(from_object, ['file_uri']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _Part_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['video_metadata']) is not None:
    setv(
        to_object,
        ['videoMetadata'],
        _VideoMetadata_to_mldev(
            getv(from_object, ['video_metadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inline_data']) is not None:
    setv(
        to_object,
        ['inlineData'],
        _Blob_to_mldev(getv(from_object, ['inline_data']), to_object),
    )

  if getv(from_object, ['file_data']) is not None:
    setv(
        to_object,
        ['fileData'],
        _FileData_to_mldev(getv(from_object, ['file_data']), to_object),
    )

  if getv(from_object, ['thought_signature']) is not None:
    setv(
        to_object,
        ['thoughtSignature'],
        getv(from_object, ['thought_signature']),
    )

  if getv(from_object, ['code_execution_result']) is not None:
    setv(
        to_object,
        ['codeExecutionResult'],
        getv(from_object, ['code_execution_result']),
    )

  if getv(from_object, ['executable_code']) is not None:
    setv(to_object, ['executableCode'], getv(from_object, ['executable_code']))

  if getv(from_object, ['function_call']) is not None:
    setv(to_object, ['functionCall'], getv(from_object, ['function_call']))

  if getv(from_object, ['function_response']) is not None:
    setv(
        to_object,
        ['functionResponse'],
        getv(from_object, ['function_response']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Content_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_to_mldev(item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _Schema_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['additional_properties']) is not None:
    raise ValueError(
        'additional_properties parameter is not supported in Gemini API.'
    )

  if getv(from_object, ['defs']) is not None:
    raise ValueError('defs parameter is not supported in Gemini API.')

  if getv(from_object, ['ref']) is not None:
    raise ValueError('ref parameter is not supported in Gemini API.')

  if getv(from_object, ['any_of']) is not None:
    setv(to_object, ['anyOf'], getv(from_object, ['any_of']))

  if getv(from_object, ['default']) is not None:
    setv(to_object, ['default'], getv(from_object, ['default']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['enum']) is not None:
    setv(to_object, ['enum'], getv(from_object, ['enum']))

  if getv(from_object, ['example']) is not None:
    setv(to_object, ['example'], getv(from_object, ['example']))

  if getv(from_object, ['format']) is not None:
    setv(to_object, ['format'], getv(from_object, ['format']))

  if getv(from_object, ['items']) is not None:
    setv(to_object, ['items'], getv(from_object, ['items']))

  if getv(from_object, ['max_items']) is not None:
    setv(to_object, ['maxItems'], getv(from_object, ['max_items']))

  if getv(from_object, ['max_length']) is not None:
    setv(to_object, ['maxLength'], getv(from_object, ['max_length']))

  if getv(from_object, ['max_properties']) is not None:
    setv(to_object, ['maxProperties'], getv(from_object, ['max_properties']))

  if getv(from_object, ['maximum']) is not None:
    setv(to_object, ['maximum'], getv(from_object, ['maximum']))

  if getv(from_object, ['min_items']) is not None:
    setv(to_object, ['minItems'], getv(from_object, ['min_items']))

  if getv(from_object, ['min_length']) is not None:
    setv(to_object, ['minLength'], getv(from_object, ['min_length']))

  if getv(from_object, ['min_properties']) is not None:
    setv(to_object, ['minProperties'], getv(from_object, ['min_properties']))

  if getv(from_object, ['minimum']) is not None:
    setv(to_object, ['minimum'], getv(from_object, ['minimum']))

  if getv(from_object, ['nullable']) is not None:
    setv(to_object, ['nullable'], getv(from_object, ['nullable']))

  if getv(from_object, ['pattern']) is not None:
    setv(to_object, ['pattern'], getv(from_object, ['pattern']))

  if getv(from_object, ['properties']) is not None:
    setv(to_object, ['properties'], getv(from_object, ['properties']))

  if getv(from_object, ['property_ordering']) is not None:
    setv(
        to_object,
        ['propertyOrdering'],
        getv(from_object, ['property_ordering']),
    )

  if getv(from_object, ['required']) is not None:
    setv(to_object, ['required'], getv(from_object, ['required']))

  if getv(from_object, ['title']) is not None:
    setv(to_object, ['title'], getv(from_object, ['title']))

  if getv(from_object, ['type']) is not None:
    setv(to_object, ['type'], getv(from_object, ['type']))

  return to_object


def _ModelSelectionConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['feature_selection_preference']) is not None:
    raise ValueError(
        'feature_selection_preference parameter is not supported in Gemini API.'
    )

  return to_object


def _SafetySetting_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['method']) is not None:
    raise ValueError('method parameter is not supported in Gemini API.')

  if getv(from_object, ['category']) is not None:
    setv(to_object, ['category'], getv(from_object, ['category']))

  if getv(from_object, ['threshold']) is not None:
    setv(to_object, ['threshold'], getv(from_object, ['threshold']))

  return to_object


def _FunctionDeclaration_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['behavior']) is not None:
    setv(to_object, ['behavior'], getv(from_object, ['behavior']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['parameters_json_schema']) is not None:
    setv(
        to_object,
        ['parametersJsonSchema'],
        getv(from_object, ['parameters_json_schema']),
    )

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  if getv(from_object, ['response_json_schema']) is not None:
    setv(
        to_object,
        ['responseJsonSchema'],
        getv(from_object, ['response_json_schema']),
    )

  return to_object


def _Interval_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['start_time']) is not None:
    setv(to_object, ['startTime'], getv(from_object, ['start_time']))

  if getv(from_object, ['end_time']) is not None:
    setv(to_object, ['endTime'], getv(from_object, ['end_time']))

  return to_object


def _GoogleSearch_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['time_range_filter']) is not None:
    setv(
        to_object,
        ['timeRangeFilter'],
        _Interval_to_mldev(getv(from_object, ['time_range_filter']), to_object),
    )

  return to_object


def _DynamicRetrievalConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamic_threshold']) is not None:
    setv(
        to_object,
        ['dynamicThreshold'],
        getv(from_object, ['dynamic_threshold']),
    )

  return to_object


def _GoogleSearchRetrieval_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamic_retrieval_config']) is not None:
    setv(
        to_object,
        ['dynamicRetrievalConfig'],
        _DynamicRetrievalConfig_to_mldev(
            getv(from_object, ['dynamic_retrieval_config']), to_object
        ),
    )

  return to_object


def _EnterpriseWebSearch_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _ApiKeyConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_string']) is not None:
    raise ValueError('api_key_string parameter is not supported in Gemini API.')

  return to_object


def _AuthConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_config']) is not None:
    raise ValueError('api_key_config parameter is not supported in Gemini API.')

  if getv(from_object, ['auth_type']) is not None:
    setv(to_object, ['authType'], getv(from_object, ['auth_type']))

  if getv(from_object, ['google_service_account_config']) is not None:
    setv(
        to_object,
        ['googleServiceAccountConfig'],
        getv(from_object, ['google_service_account_config']),
    )

  if getv(from_object, ['http_basic_auth_config']) is not None:
    setv(
        to_object,
        ['httpBasicAuthConfig'],
        getv(from_object, ['http_basic_auth_config']),
    )

  if getv(from_object, ['oauth_config']) is not None:
    setv(to_object, ['oauthConfig'], getv(from_object, ['oauth_config']))

  if getv(from_object, ['oidc_config']) is not None:
    setv(to_object, ['oidcConfig'], getv(from_object, ['oidc_config']))

  return to_object


def _GoogleMaps_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['auth_config']) is not None:
    raise ValueError('auth_config parameter is not supported in Gemini API.')

  return to_object


def _UrlContext_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _Tool_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_declarations']) is not None:
    setv(
        to_object,
        ['functionDeclarations'],
        [
            _FunctionDeclaration_to_mldev(item, to_object)
            for item in getv(from_object, ['function_declarations'])
        ],
    )

  if getv(from_object, ['retrieval']) is not None:
    raise ValueError('retrieval parameter is not supported in Gemini API.')

  if getv(from_object, ['google_search']) is not None:
    setv(
        to_object,
        ['googleSearch'],
        _GoogleSearch_to_mldev(getv(from_object, ['google_search']), to_object),
    )

  if getv(from_object, ['google_search_retrieval']) is not None:
    setv(
        to_object,
        ['googleSearchRetrieval'],
        _GoogleSearchRetrieval_to_mldev(
            getv(from_object, ['google_search_retrieval']), to_object
        ),
    )

  if getv(from_object, ['enterprise_web_search']) is not None:
    raise ValueError(
        'enterprise_web_search parameter is not supported in Gemini API.'
    )

  if getv(from_object, ['google_maps']) is not None:
    raise ValueError('google_maps parameter is not supported in Gemini API.')

  if getv(from_object, ['url_context']) is not None:
    setv(
        to_object,
        ['urlContext'],
        _UrlContext_to_mldev(getv(from_object, ['url_context']), to_object),
    )

  if getv(from_object, ['code_execution']) is not None:
    setv(to_object, ['codeExecution'], getv(from_object, ['code_execution']))

  if getv(from_object, ['computer_use']) is not None:
    setv(to_object, ['computerUse'], getv(from_object, ['computer_use']))

  return to_object


def _FunctionCallingConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['allowed_function_names']) is not None:
    setv(
        to_object,
        ['allowedFunctionNames'],
        getv(from_object, ['allowed_function_names']),
    )

  return to_object


def _LatLng_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latitude']) is not None:
    setv(to_object, ['latitude'], getv(from_object, ['latitude']))

  if getv(from_object, ['longitude']) is not None:
    setv(to_object, ['longitude'], getv(from_object, ['longitude']))

  return to_object


def _RetrievalConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['lat_lng']) is not None:
    setv(
        to_object,
        ['latLng'],
        _LatLng_to_mldev(getv(from_object, ['lat_lng']), to_object),
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _ToolConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_calling_config']) is not None:
    setv(
        to_object,
        ['functionCallingConfig'],
        _FunctionCallingConfig_to_mldev(
            getv(from_object, ['function_calling_config']), to_object
        ),
    )

  if getv(from_object, ['retrieval_config']) is not None:
    setv(
        to_object,
        ['retrievalConfig'],
        _RetrievalConfig_to_mldev(
            getv(from_object, ['retrieval_config']), to_object
        ),
    )

  return to_object


def _PrebuiltVoiceConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_name']) is not None:
    setv(to_object, ['voiceName'], getv(from_object, ['voice_name']))

  return to_object


def _VoiceConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuilt_voice_config']) is not None:
    setv(
        to_object,
        ['prebuiltVoiceConfig'],
        _PrebuiltVoiceConfig_to_mldev(
            getv(from_object, ['prebuilt_voice_config']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker']) is not None:
    setv(to_object, ['speaker'], getv(from_object, ['speaker']))

  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_mldev(getv(from_object, ['voice_config']), to_object),
    )

  return to_object


def _MultiSpeakerVoiceConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker_voice_configs']) is not None:
    setv(
        to_object,
        ['speakerVoiceConfigs'],
        [
            _SpeakerVoiceConfig_to_mldev(item, to_object)
            for item in getv(from_object, ['speaker_voice_configs'])
        ],
    )

  return to_object


def _SpeechConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_mldev(getv(from_object, ['voice_config']), to_object),
    )

  if getv(from_object, ['multi_speaker_voice_config']) is not None:
    setv(
        to_object,
        ['multiSpeakerVoiceConfig'],
        _MultiSpeakerVoiceConfig_to_mldev(
            getv(from_object, ['multi_speaker_voice_config']), to_object
        ),
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _ThinkingConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['include_thoughts']) is not None:
    setv(
        to_object, ['includeThoughts'], getv(from_object, ['include_thoughts'])
    )

  if getv(from_object, ['thinking_budget']) is not None:
    setv(to_object, ['thinkingBudget'], getv(from_object, ['thinking_budget']))

  return to_object


def _GenerateContentConfig_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['system_instruction']) is not None:
    setv(
        parent_object,
        ['systemInstruction'],
        _Content_to_mldev(
            t.t_content(getv(from_object, ['system_instruction'])), to_object
        ),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(to_object, ['temperature'], getv(from_object, ['temperature']))

  if getv(from_object, ['top_p']) is not None:
    setv(to_object, ['topP'], getv(from_object, ['top_p']))

  if getv(from_object, ['top_k']) is not None:
    setv(to_object, ['topK'], getv(from_object, ['top_k']))

  if getv(from_object, ['candidate_count']) is not None:
    setv(to_object, ['candidateCount'], getv(from_object, ['candidate_count']))

  if getv(from_object, ['max_output_tokens']) is not None:
    setv(
        to_object, ['maxOutputTokens'], getv(from_object, ['max_output_tokens'])
    )

  if getv(from_object, ['stop_sequences']) is not None:
    setv(to_object, ['stopSequences'], getv(from_object, ['stop_sequences']))

  if getv(from_object, ['response_logprobs']) is not None:
    setv(
        to_object,
        ['responseLogprobs'],
        getv(from_object, ['response_logprobs']),
    )

  if getv(from_object, ['logprobs']) is not None:
    setv(to_object, ['logprobs'], getv(from_object, ['logprobs']))

  if getv(from_object, ['presence_penalty']) is not None:
    setv(
        to_object, ['presencePenalty'], getv(from_object, ['presence_penalty'])
    )

  if getv(from_object, ['frequency_penalty']) is not None:
    setv(
        to_object,
        ['frequencyPenalty'],
        getv(from_object, ['frequency_penalty']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(to_object, ['seed'], getv(from_object, ['seed']))

  if getv(from_object, ['response_mime_type']) is not None:
    setv(
        to_object,
        ['responseMimeType'],
        getv(from_object, ['response_mime_type']),
    )

  if getv(from_object, ['response_schema']) is not None:
    setv(
        to_object,
        ['responseSchema'],
        _Schema_to_mldev(
            t.t_schema(api_client, getv(from_object, ['response_schema'])),
            to_object,
        ),
    )

  if getv(from_object, ['response_json_schema']) is not None:
    setv(
        to_object,
        ['responseJsonSchema'],
        getv(from_object, ['response_json_schema']),
    )

  if getv(from_object, ['routing_config']) is not None:
    raise ValueError('routing_config parameter is not supported in Gemini API.')

  if getv(from_object, ['model_selection_config']) is not None:
    raise ValueError(
        'model_selection_config parameter is not supported in Gemini API.'
    )

  if getv(from_object, ['safety_settings']) is not None:
    setv(
        parent_object,
        ['safetySettings'],
        [
            _SafetySetting_to_mldev(item, to_object)
            for item in getv(from_object, ['safety_settings'])
        ],
    )

  if getv(from_object, ['tools']) is not None:
    setv(
        parent_object,
        ['tools'],
        [
            _Tool_to_mldev(t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(from_object, ['tools']))
        ],
    )

  if getv(from_object, ['tool_config']) is not None:
    setv(
        parent_object,
        ['toolConfig'],
        _ToolConfig_to_mldev(getv(from_object, ['tool_config']), to_object),
    )

  if getv(from_object, ['labels']) is not None:
    raise ValueError('labels parameter is not supported in Gemini API.')

  if getv(from_object, ['cached_content']) is not None:
    setv(
        parent_object,
        ['cachedContent'],
        t.t_cached_content_name(
            api_client, getv(from_object, ['cached_content'])
        ),
    )

  if getv(from_object, ['response_modalities']) is not None:
    setv(
        to_object,
        ['responseModalities'],
        getv(from_object, ['response_modalities']),
    )

  if getv(from_object, ['media_resolution']) is not None:
    setv(
        to_object, ['mediaResolution'], getv(from_object, ['media_resolution'])
    )

  if getv(from_object, ['speech_config']) is not None:
    setv(
        to_object,
        ['speechConfig'],
        _SpeechConfig_to_mldev(
            t.t_speech_config(getv(from_object, ['speech_config'])), to_object
        ),
    )

  if getv(from_object, ['audio_timestamp']) is not None:
    raise ValueError(
        'audio_timestamp parameter is not supported in Gemini API.'
    )

  if getv(from_object, ['thinking_config']) is not None:
    setv(
        to_object,
        ['thinkingConfig'],
        _ThinkingConfig_to_mldev(
            getv(from_object, ['thinking_config']), to_object
        ),
    )

  return to_object


def _InlinedRequest_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    setv(
        to_object,
        ['request', 'model'],
        t.t_model(api_client, getv(from_object, ['model'])),
    )

  if getv(from_object, ['contents']) is not None:
    setv(
        to_object,
        ['request', 'contents'],
        [
            _Content_to_mldev(item, to_object)
            for item in t.t_contents(getv(from_object, ['contents']))
        ],
    )

  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['request', 'generationConfig'],
        _GenerateContentConfig_to_mldev(
            api_client, getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _BatchJobSource_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['format']) is not None:
    raise ValueError('format parameter is not supported in Gemini API.')

  if getv(from_object, ['gcs_uri']) is not None:
    raise ValueError('gcs_uri parameter is not supported in Gemini API.')

  if getv(from_object, ['bigquery_uri']) is not None:
    raise ValueError('bigquery_uri parameter is not supported in Gemini API.')

  if getv(from_object, ['file_name']) is not None:
    setv(to_object, ['fileName'], getv(from_object, ['file_name']))

  if getv(from_object, ['inlined_requests']) is not None:
    setv(
        to_object,
        ['requests', 'requests'],
        [
            _InlinedRequest_to_mldev(api_client, item, to_object)
            for item in getv(from_object, ['inlined_requests'])
        ],
    )

  return to_object


def _CitationMetadata_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['citations']) is not None:
    setv(to_object, ['citationSources'], getv(from_object, ['citations']))

  return to_object


def _UrlMetadata_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['retrieved_url']) is not None:
    setv(to_object, ['retrievedUrl'], getv(from_object, ['retrieved_url']))

  if getv(from_object, ['url_retrieval_status']) is not None:
    setv(
        to_object,
        ['urlRetrievalStatus'],
        getv(from_object, ['url_retrieval_status']),
    )

  return to_object


def _UrlContextMetadata_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['url_metadata']) is not None:
    setv(
        to_object,
        ['urlMetadata'],
        [
            _UrlMetadata_to_mldev(item, to_object)
            for item in getv(from_object, ['url_metadata'])
        ],
    )

  return to_object


def _Candidate_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['content']) is not None:
    setv(
        to_object,
        ['content'],
        _Content_to_mldev(getv(from_object, ['content']), to_object),
    )

  if getv(from_object, ['citation_metadata']) is not None:
    setv(
        to_object,
        ['citationMetadata'],
        _CitationMetadata_to_mldev(
            getv(from_object, ['citation_metadata']), to_object
        ),
    )

  if getv(from_object, ['finish_message']) is not None:
    raise ValueError('finish_message parameter is not supported in Gemini API.')

  if getv(from_object, ['token_count']) is not None:
    setv(to_object, ['tokenCount'], getv(from_object, ['token_count']))

  if getv(from_object, ['finish_reason']) is not None:
    _FinishReason_to_mldev_enum_validate(getv(from_object, ['finish_reason']))
    setv(to_object, ['finishReason'], getv(from_object, ['finish_reason']))

  if getv(from_object, ['url_context_metadata']) is not None:
    setv(
        to_object,
        ['urlContextMetadata'],
        _UrlContextMetadata_to_mldev(
            getv(from_object, ['url_context_metadata']), to_object
        ),
    )

  if getv(from_object, ['avg_logprobs']) is not None:
    setv(to_object, ['avgLogprobs'], getv(from_object, ['avg_logprobs']))

  if getv(from_object, ['grounding_metadata']) is not None:
    setv(
        to_object,
        ['groundingMetadata'],
        getv(from_object, ['grounding_metadata']),
    )

  if getv(from_object, ['index']) is not None:
    setv(to_object, ['index'], getv(from_object, ['index']))

  if getv(from_object, ['logprobs_result']) is not None:
    setv(to_object, ['logprobsResult'], getv(from_object, ['logprobs_result']))

  if getv(from_object, ['safety_ratings']) is not None:
    setv(to_object, ['safetyRatings'], getv(from_object, ['safety_ratings']))

  return to_object


def _GenerateContentResponse_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['sdk_http_response']) is not None:
    setv(
        to_object, ['sdkHttpResponse'], getv(from_object, ['sdk_http_response'])
    )

  if getv(from_object, ['candidates']) is not None:
    setv(
        to_object,
        ['candidates'],
        [
            _Candidate_to_mldev(item, to_object)
            for item in getv(from_object, ['candidates'])
        ],
    )

  if getv(from_object, ['create_time']) is not None:
    raise ValueError('create_time parameter is not supported in Gemini API.')

  if getv(from_object, ['response_id']) is not None:
    raise ValueError('response_id parameter is not supported in Gemini API.')

  if getv(from_object, ['model_version']) is not None:
    setv(to_object, ['modelVersion'], getv(from_object, ['model_version']))

  if getv(from_object, ['prompt_feedback']) is not None:
    setv(to_object, ['promptFeedback'], getv(from_object, ['prompt_feedback']))

  if getv(from_object, ['usage_metadata']) is not None:
    setv(to_object, ['usageMetadata'], getv(from_object, ['usage_metadata']))

  return to_object


def _JobError_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['details']) is not None:
    raise ValueError('details parameter is not supported in Gemini API.')

  if getv(from_object, ['code']) is not None:
    raise ValueError('code parameter is not supported in Gemini API.')

  if getv(from_object, ['message']) is not None:
    raise ValueError('message parameter is not supported in Gemini API.')

  return to_object


def _InlinedResponse_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['response']) is not None:
    setv(
        to_object,
        ['response'],
        _GenerateContentResponse_to_mldev(
            getv(from_object, ['response']), to_object
        ),
    )

  if getv(from_object, ['error']) is not None:
    setv(
        to_object,
        ['error'],
        _JobError_to_mldev(getv(from_object, ['error']), to_object),
    )

  return to_object


def _BatchJobDestination_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['format']) is not None:
    raise ValueError('format parameter is not supported in Gemini API.')

  if getv(from_object, ['gcs_uri']) is not None:
    raise ValueError('gcs_uri parameter is not supported in Gemini API.')

  if getv(from_object, ['bigquery_uri']) is not None:
    raise ValueError('bigquery_uri parameter is not supported in Gemini API.')

  if getv(from_object, ['file_name']) is not None:
    setv(to_object, ['responsesFile'], getv(from_object, ['file_name']))

  if getv(from_object, ['inlined_responses']) is not None:
    setv(
        to_object,
        ['inlinedResponses', 'inlinedResponses'],
        [
            _InlinedResponse_to_mldev(item, to_object)
            for item in getv(from_object, ['inlined_responses'])
        ],
    )

  return to_object


def _CreateBatchJobConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['display_name']) is not None:
    setv(
        parent_object,
        ['batch', 'displayName'],
        getv(from_object, ['display_name']),
    )

  if getv(from_object, ['dest']) is not None:
    raise ValueError('dest parameter is not supported in Gemini API.')

  return to_object


def _CreateBatchJobParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    setv(
        to_object,
        ['_url', 'model'],
        t.t_model(api_client, getv(from_object, ['model'])),
    )

  if getv(from_object, ['src']) is not None:
    setv(
        to_object,
        ['batch', 'inputConfig'],
        _BatchJobSource_to_mldev(
            api_client,
            t.t_batch_job_source(api_client, getv(from_object, ['src'])),
            to_object,
        ),
    )

  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _CreateBatchJobConfig_to_mldev(
            getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _GetBatchJobParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _CancelBatchJobParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _ListBatchJobsConfig_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['page_size']) is not None:
    setv(
        parent_object, ['_query', 'pageSize'], getv(from_object, ['page_size'])
    )

  if getv(from_object, ['page_token']) is not None:
    setv(
        parent_object,
        ['_query', 'pageToken'],
        getv(from_object, ['page_token']),
    )

  if getv(from_object, ['filter']) is not None:
    raise ValueError('filter parameter is not supported in Gemini API.')

  return to_object


def _ListBatchJobsParameters_to_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _ListBatchJobsConfig_to_mldev(getv(from_object, ['config']), to_object),
    )

  return to_object


def _DeleteBatchJobParameters_to_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _VideoMetadata_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['end_offset']) is not None:
    setv(to_object, ['endOffset'], getv(from_object, ['end_offset']))

  if getv(from_object, ['start_offset']) is not None:
    setv(to_object, ['startOffset'], getv(from_object, ['start_offset']))

  return to_object


def _Blob_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    setv(to_object, ['displayName'], getv(from_object, ['display_name']))

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _FileData_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['display_name']) is not None:
    setv(to_object, ['displayName'], getv(from_object, ['display_name']))

  if getv(from_object, ['file_uri']) is not None:
    setv(to_object, ['fileUri'], getv(from_object, ['file_uri']))

  if getv(from_object, ['mime_type']) is not None:
    setv(to_object, ['mimeType'], getv(from_object, ['mime_type']))

  return to_object


def _Part_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['video_metadata']) is not None:
    setv(
        to_object,
        ['videoMetadata'],
        _VideoMetadata_to_vertex(
            getv(from_object, ['video_metadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inline_data']) is not None:
    setv(
        to_object,
        ['inlineData'],
        _Blob_to_vertex(getv(from_object, ['inline_data']), to_object),
    )

  if getv(from_object, ['file_data']) is not None:
    setv(
        to_object,
        ['fileData'],
        _FileData_to_vertex(getv(from_object, ['file_data']), to_object),
    )

  if getv(from_object, ['thought_signature']) is not None:
    setv(
        to_object,
        ['thoughtSignature'],
        getv(from_object, ['thought_signature']),
    )

  if getv(from_object, ['code_execution_result']) is not None:
    setv(
        to_object,
        ['codeExecutionResult'],
        getv(from_object, ['code_execution_result']),
    )

  if getv(from_object, ['executable_code']) is not None:
    setv(to_object, ['executableCode'], getv(from_object, ['executable_code']))

  if getv(from_object, ['function_call']) is not None:
    setv(to_object, ['functionCall'], getv(from_object, ['function_call']))

  if getv(from_object, ['function_response']) is not None:
    setv(
        to_object,
        ['functionResponse'],
        getv(from_object, ['function_response']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Content_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_to_vertex(item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _Schema_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['additional_properties']) is not None:
    setv(
        to_object,
        ['additionalProperties'],
        getv(from_object, ['additional_properties']),
    )

  if getv(from_object, ['defs']) is not None:
    setv(to_object, ['defs'], getv(from_object, ['defs']))

  if getv(from_object, ['ref']) is not None:
    setv(to_object, ['ref'], getv(from_object, ['ref']))

  if getv(from_object, ['any_of']) is not None:
    setv(to_object, ['anyOf'], getv(from_object, ['any_of']))

  if getv(from_object, ['default']) is not None:
    setv(to_object, ['default'], getv(from_object, ['default']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['enum']) is not None:
    setv(to_object, ['enum'], getv(from_object, ['enum']))

  if getv(from_object, ['example']) is not None:
    setv(to_object, ['example'], getv(from_object, ['example']))

  if getv(from_object, ['format']) is not None:
    setv(to_object, ['format'], getv(from_object, ['format']))

  if getv(from_object, ['items']) is not None:
    setv(to_object, ['items'], getv(from_object, ['items']))

  if getv(from_object, ['max_items']) is not None:
    setv(to_object, ['maxItems'], getv(from_object, ['max_items']))

  if getv(from_object, ['max_length']) is not None:
    setv(to_object, ['maxLength'], getv(from_object, ['max_length']))

  if getv(from_object, ['max_properties']) is not None:
    setv(to_object, ['maxProperties'], getv(from_object, ['max_properties']))

  if getv(from_object, ['maximum']) is not None:
    setv(to_object, ['maximum'], getv(from_object, ['maximum']))

  if getv(from_object, ['min_items']) is not None:
    setv(to_object, ['minItems'], getv(from_object, ['min_items']))

  if getv(from_object, ['min_length']) is not None:
    setv(to_object, ['minLength'], getv(from_object, ['min_length']))

  if getv(from_object, ['min_properties']) is not None:
    setv(to_object, ['minProperties'], getv(from_object, ['min_properties']))

  if getv(from_object, ['minimum']) is not None:
    setv(to_object, ['minimum'], getv(from_object, ['minimum']))

  if getv(from_object, ['nullable']) is not None:
    setv(to_object, ['nullable'], getv(from_object, ['nullable']))

  if getv(from_object, ['pattern']) is not None:
    setv(to_object, ['pattern'], getv(from_object, ['pattern']))

  if getv(from_object, ['properties']) is not None:
    setv(to_object, ['properties'], getv(from_object, ['properties']))

  if getv(from_object, ['property_ordering']) is not None:
    setv(
        to_object,
        ['propertyOrdering'],
        getv(from_object, ['property_ordering']),
    )

  if getv(from_object, ['required']) is not None:
    setv(to_object, ['required'], getv(from_object, ['required']))

  if getv(from_object, ['title']) is not None:
    setv(to_object, ['title'], getv(from_object, ['title']))

  if getv(from_object, ['type']) is not None:
    setv(to_object, ['type'], getv(from_object, ['type']))

  return to_object


def _ModelSelectionConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['feature_selection_preference']) is not None:
    setv(
        to_object,
        ['featureSelectionPreference'],
        getv(from_object, ['feature_selection_preference']),
    )

  return to_object


def _SafetySetting_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['method']) is not None:
    setv(to_object, ['method'], getv(from_object, ['method']))

  if getv(from_object, ['category']) is not None:
    setv(to_object, ['category'], getv(from_object, ['category']))

  if getv(from_object, ['threshold']) is not None:
    setv(to_object, ['threshold'], getv(from_object, ['threshold']))

  return to_object


def _FunctionDeclaration_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['behavior']) is not None:
    raise ValueError('behavior parameter is not supported in Vertex AI.')

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['parameters_json_schema']) is not None:
    setv(
        to_object,
        ['parametersJsonSchema'],
        getv(from_object, ['parameters_json_schema']),
    )

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  if getv(from_object, ['response_json_schema']) is not None:
    setv(
        to_object,
        ['responseJsonSchema'],
        getv(from_object, ['response_json_schema']),
    )

  return to_object


def _Interval_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['start_time']) is not None:
    setv(to_object, ['startTime'], getv(from_object, ['start_time']))

  if getv(from_object, ['end_time']) is not None:
    setv(to_object, ['endTime'], getv(from_object, ['end_time']))

  return to_object


def _GoogleSearch_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['time_range_filter']) is not None:
    setv(
        to_object,
        ['timeRangeFilter'],
        _Interval_to_vertex(
            getv(from_object, ['time_range_filter']), to_object
        ),
    )

  return to_object


def _DynamicRetrievalConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamic_threshold']) is not None:
    setv(
        to_object,
        ['dynamicThreshold'],
        getv(from_object, ['dynamic_threshold']),
    )

  return to_object


def _GoogleSearchRetrieval_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamic_retrieval_config']) is not None:
    setv(
        to_object,
        ['dynamicRetrievalConfig'],
        _DynamicRetrievalConfig_to_vertex(
            getv(from_object, ['dynamic_retrieval_config']), to_object
        ),
    )

  return to_object


def _EnterpriseWebSearch_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _ApiKeyConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_string']) is not None:
    setv(to_object, ['apiKeyString'], getv(from_object, ['api_key_string']))

  return to_object


def _AuthConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['api_key_config']) is not None:
    setv(
        to_object,
        ['apiKeyConfig'],
        _ApiKeyConfig_to_vertex(
            getv(from_object, ['api_key_config']), to_object
        ),
    )

  if getv(from_object, ['auth_type']) is not None:
    setv(to_object, ['authType'], getv(from_object, ['auth_type']))

  if getv(from_object, ['google_service_account_config']) is not None:
    setv(
        to_object,
        ['googleServiceAccountConfig'],
        getv(from_object, ['google_service_account_config']),
    )

  if getv(from_object, ['http_basic_auth_config']) is not None:
    setv(
        to_object,
        ['httpBasicAuthConfig'],
        getv(from_object, ['http_basic_auth_config']),
    )

  if getv(from_object, ['oauth_config']) is not None:
    setv(to_object, ['oauthConfig'], getv(from_object, ['oauth_config']))

  if getv(from_object, ['oidc_config']) is not None:
    setv(to_object, ['oidcConfig'], getv(from_object, ['oidc_config']))

  return to_object


def _GoogleMaps_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['auth_config']) is not None:
    setv(
        to_object,
        ['authConfig'],
        _AuthConfig_to_vertex(getv(from_object, ['auth_config']), to_object),
    )

  return to_object


def _UrlContext_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _Tool_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_declarations']) is not None:
    setv(
        to_object,
        ['functionDeclarations'],
        [
            _FunctionDeclaration_to_vertex(item, to_object)
            for item in getv(from_object, ['function_declarations'])
        ],
    )

  if getv(from_object, ['retrieval']) is not None:
    setv(to_object, ['retrieval'], getv(from_object, ['retrieval']))

  if getv(from_object, ['google_search']) is not None:
    setv(
        to_object,
        ['googleSearch'],
        _GoogleSearch_to_vertex(
            getv(from_object, ['google_search']), to_object
        ),
    )

  if getv(from_object, ['google_search_retrieval']) is not None:
    setv(
        to_object,
        ['googleSearchRetrieval'],
        _GoogleSearchRetrieval_to_vertex(
            getv(from_object, ['google_search_retrieval']), to_object
        ),
    )

  if getv(from_object, ['enterprise_web_search']) is not None:
    setv(
        to_object,
        ['enterpriseWebSearch'],
        _EnterpriseWebSearch_to_vertex(
            getv(from_object, ['enterprise_web_search']), to_object
        ),
    )

  if getv(from_object, ['google_maps']) is not None:
    setv(
        to_object,
        ['googleMaps'],
        _GoogleMaps_to_vertex(getv(from_object, ['google_maps']), to_object),
    )

  if getv(from_object, ['url_context']) is not None:
    setv(
        to_object,
        ['urlContext'],
        _UrlContext_to_vertex(getv(from_object, ['url_context']), to_object),
    )

  if getv(from_object, ['code_execution']) is not None:
    setv(to_object, ['codeExecution'], getv(from_object, ['code_execution']))

  if getv(from_object, ['computer_use']) is not None:
    setv(to_object, ['computerUse'], getv(from_object, ['computer_use']))

  return to_object


def _FunctionCallingConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['allowed_function_names']) is not None:
    setv(
        to_object,
        ['allowedFunctionNames'],
        getv(from_object, ['allowed_function_names']),
    )

  return to_object


def _LatLng_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latitude']) is not None:
    setv(to_object, ['latitude'], getv(from_object, ['latitude']))

  if getv(from_object, ['longitude']) is not None:
    setv(to_object, ['longitude'], getv(from_object, ['longitude']))

  return to_object


def _RetrievalConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['lat_lng']) is not None:
    setv(
        to_object,
        ['latLng'],
        _LatLng_to_vertex(getv(from_object, ['lat_lng']), to_object),
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _ToolConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['function_calling_config']) is not None:
    setv(
        to_object,
        ['functionCallingConfig'],
        _FunctionCallingConfig_to_vertex(
            getv(from_object, ['function_calling_config']), to_object
        ),
    )

  if getv(from_object, ['retrieval_config']) is not None:
    setv(
        to_object,
        ['retrievalConfig'],
        _RetrievalConfig_to_vertex(
            getv(from_object, ['retrieval_config']), to_object
        ),
    )

  return to_object


def _PrebuiltVoiceConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_name']) is not None:
    setv(to_object, ['voiceName'], getv(from_object, ['voice_name']))

  return to_object


def _VoiceConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuilt_voice_config']) is not None:
    setv(
        to_object,
        ['prebuiltVoiceConfig'],
        _PrebuiltVoiceConfig_to_vertex(
            getv(from_object, ['prebuilt_voice_config']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker']) is not None:
    raise ValueError('speaker parameter is not supported in Vertex AI.')

  if getv(from_object, ['voice_config']) is not None:
    raise ValueError('voice_config parameter is not supported in Vertex AI.')

  return to_object


def _MultiSpeakerVoiceConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker_voice_configs']) is not None:
    raise ValueError(
        'speaker_voice_configs parameter is not supported in Vertex AI.'
    )

  return to_object


def _SpeechConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voice_config']) is not None:
    setv(
        to_object,
        ['voiceConfig'],
        _VoiceConfig_to_vertex(getv(from_object, ['voice_config']), to_object),
    )

  if getv(from_object, ['multi_speaker_voice_config']) is not None:
    raise ValueError(
        'multi_speaker_voice_config parameter is not supported in Vertex AI.'
    )

  if getv(from_object, ['language_code']) is not None:
    setv(to_object, ['languageCode'], getv(from_object, ['language_code']))

  return to_object


def _ThinkingConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['include_thoughts']) is not None:
    setv(
        to_object, ['includeThoughts'], getv(from_object, ['include_thoughts'])
    )

  if getv(from_object, ['thinking_budget']) is not None:
    setv(to_object, ['thinkingBudget'], getv(from_object, ['thinking_budget']))

  return to_object


def _GenerateContentConfig_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['system_instruction']) is not None:
    setv(
        parent_object,
        ['systemInstruction'],
        _Content_to_vertex(
            t.t_content(getv(from_object, ['system_instruction'])), to_object
        ),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(to_object, ['temperature'], getv(from_object, ['temperature']))

  if getv(from_object, ['top_p']) is not None:
    setv(to_object, ['topP'], getv(from_object, ['top_p']))

  if getv(from_object, ['top_k']) is not None:
    setv(to_object, ['topK'], getv(from_object, ['top_k']))

  if getv(from_object, ['candidate_count']) is not None:
    setv(to_object, ['candidateCount'], getv(from_object, ['candidate_count']))

  if getv(from_object, ['max_output_tokens']) is not None:
    setv(
        to_object, ['maxOutputTokens'], getv(from_object, ['max_output_tokens'])
    )

  if getv(from_object, ['stop_sequences']) is not None:
    setv(to_object, ['stopSequences'], getv(from_object, ['stop_sequences']))

  if getv(from_object, ['response_logprobs']) is not None:
    setv(
        to_object,
        ['responseLogprobs'],
        getv(from_object, ['response_logprobs']),
    )

  if getv(from_object, ['logprobs']) is not None:
    setv(to_object, ['logprobs'], getv(from_object, ['logprobs']))

  if getv(from_object, ['presence_penalty']) is not None:
    setv(
        to_object, ['presencePenalty'], getv(from_object, ['presence_penalty'])
    )

  if getv(from_object, ['frequency_penalty']) is not None:
    setv(
        to_object,
        ['frequencyPenalty'],
        getv(from_object, ['frequency_penalty']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(to_object, ['seed'], getv(from_object, ['seed']))

  if getv(from_object, ['response_mime_type']) is not None:
    setv(
        to_object,
        ['responseMimeType'],
        getv(from_object, ['response_mime_type']),
    )

  if getv(from_object, ['response_schema']) is not None:
    setv(
        to_object,
        ['responseSchema'],
        _Schema_to_vertex(
            t.t_schema(api_client, getv(from_object, ['response_schema'])),
            to_object,
        ),
    )

  if getv(from_object, ['response_json_schema']) is not None:
    setv(
        to_object,
        ['responseJsonSchema'],
        getv(from_object, ['response_json_schema']),
    )

  if getv(from_object, ['routing_config']) is not None:
    setv(to_object, ['routingConfig'], getv(from_object, ['routing_config']))

  if getv(from_object, ['model_selection_config']) is not None:
    setv(
        to_object,
        ['modelConfig'],
        _ModelSelectionConfig_to_vertex(
            getv(from_object, ['model_selection_config']), to_object
        ),
    )

  if getv(from_object, ['safety_settings']) is not None:
    setv(
        parent_object,
        ['safetySettings'],
        [
            _SafetySetting_to_vertex(item, to_object)
            for item in getv(from_object, ['safety_settings'])
        ],
    )

  if getv(from_object, ['tools']) is not None:
    setv(
        parent_object,
        ['tools'],
        [
            _Tool_to_vertex(t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(from_object, ['tools']))
        ],
    )

  if getv(from_object, ['tool_config']) is not None:
    setv(
        parent_object,
        ['toolConfig'],
        _ToolConfig_to_vertex(getv(from_object, ['tool_config']), to_object),
    )

  if getv(from_object, ['labels']) is not None:
    setv(parent_object, ['labels'], getv(from_object, ['labels']))

  if getv(from_object, ['cached_content']) is not None:
    setv(
        parent_object,
        ['cachedContent'],
        t.t_cached_content_name(
            api_client, getv(from_object, ['cached_content'])
        ),
    )

  if getv(from_object, ['response_modalities']) is not None:
    setv(
        to_object,
        ['responseModalities'],
        getv(from_object, ['response_modalities']),
    )

  if getv(from_object, ['media_resolution']) is not None:
    setv(
        to_object, ['mediaResolution'], getv(from_object, ['media_resolution'])
    )

  if getv(from_object, ['speech_config']) is not None:
    setv(
        to_object,
        ['speechConfig'],
        _SpeechConfig_to_vertex(
            t.t_speech_config(getv(from_object, ['speech_config'])), to_object
        ),
    )

  if getv(from_object, ['audio_timestamp']) is not None:
    setv(to_object, ['audioTimestamp'], getv(from_object, ['audio_timestamp']))

  if getv(from_object, ['thinking_config']) is not None:
    setv(
        to_object,
        ['thinkingConfig'],
        _ThinkingConfig_to_vertex(
            getv(from_object, ['thinking_config']), to_object
        ),
    )

  return to_object


def _InlinedRequest_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    raise ValueError('model parameter is not supported in Vertex AI.')

  if getv(from_object, ['contents']) is not None:
    raise ValueError('contents parameter is not supported in Vertex AI.')

  if getv(from_object, ['config']) is not None:
    raise ValueError('config parameter is not supported in Vertex AI.')

  return to_object


def _BatchJobSource_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['format']) is not None:
    setv(to_object, ['instancesFormat'], getv(from_object, ['format']))

  if getv(from_object, ['gcs_uri']) is not None:
    setv(to_object, ['gcsSource', 'uris'], getv(from_object, ['gcs_uri']))

  if getv(from_object, ['bigquery_uri']) is not None:
    setv(
        to_object,
        ['bigquerySource', 'inputUri'],
        getv(from_object, ['bigquery_uri']),
    )

  if getv(from_object, ['file_name']) is not None:
    raise ValueError('file_name parameter is not supported in Vertex AI.')

  if getv(from_object, ['inlined_requests']) is not None:
    raise ValueError(
        'inlined_requests parameter is not supported in Vertex AI.'
    )

  return to_object


def _CitationMetadata_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['citations']) is not None:
    setv(to_object, ['citations'], getv(from_object, ['citations']))

  return to_object


def _UrlMetadata_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['retrieved_url']) is not None:
    setv(to_object, ['retrievedUrl'], getv(from_object, ['retrieved_url']))

  if getv(from_object, ['url_retrieval_status']) is not None:
    setv(
        to_object,
        ['urlRetrievalStatus'],
        getv(from_object, ['url_retrieval_status']),
    )

  return to_object


def _UrlContextMetadata_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['url_metadata']) is not None:
    setv(
        to_object,
        ['urlMetadata'],
        [
            _UrlMetadata_to_vertex(item, to_object)
            for item in getv(from_object, ['url_metadata'])
        ],
    )

  return to_object


def _Candidate_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['content']) is not None:
    setv(
        to_object,
        ['content'],
        _Content_to_vertex(getv(from_object, ['content']), to_object),
    )

  if getv(from_object, ['citation_metadata']) is not None:
    setv(
        to_object,
        ['citationMetadata'],
        _CitationMetadata_to_vertex(
            getv(from_object, ['citation_metadata']), to_object
        ),
    )

  if getv(from_object, ['finish_message']) is not None:
    setv(to_object, ['finishMessage'], getv(from_object, ['finish_message']))

  if getv(from_object, ['token_count']) is not None:
    raise ValueError('token_count parameter is not supported in Vertex AI.')

  if getv(from_object, ['finish_reason']) is not None:
    _FinishReason_to_vertex_enum_validate(getv(from_object, ['finish_reason']))
    setv(to_object, ['finishReason'], getv(from_object, ['finish_reason']))

  if getv(from_object, ['url_context_metadata']) is not None:
    setv(
        to_object,
        ['urlContextMetadata'],
        _UrlContextMetadata_to_vertex(
            getv(from_object, ['url_context_metadata']), to_object
        ),
    )

  if getv(from_object, ['avg_logprobs']) is not None:
    setv(to_object, ['avgLogprobs'], getv(from_object, ['avg_logprobs']))

  if getv(from_object, ['grounding_metadata']) is not None:
    setv(
        to_object,
        ['groundingMetadata'],
        getv(from_object, ['grounding_metadata']),
    )

  if getv(from_object, ['index']) is not None:
    setv(to_object, ['index'], getv(from_object, ['index']))

  if getv(from_object, ['logprobs_result']) is not None:
    setv(to_object, ['logprobsResult'], getv(from_object, ['logprobs_result']))

  if getv(from_object, ['safety_ratings']) is not None:
    setv(to_object, ['safetyRatings'], getv(from_object, ['safety_ratings']))

  return to_object


def _GenerateContentResponse_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['sdk_http_response']) is not None:
    setv(
        to_object, ['sdkHttpResponse'], getv(from_object, ['sdk_http_response'])
    )

  if getv(from_object, ['candidates']) is not None:
    setv(
        to_object,
        ['candidates'],
        [
            _Candidate_to_vertex(item, to_object)
            for item in getv(from_object, ['candidates'])
        ],
    )

  if getv(from_object, ['create_time']) is not None:
    setv(to_object, ['createTime'], getv(from_object, ['create_time']))

  if getv(from_object, ['response_id']) is not None:
    setv(to_object, ['responseId'], getv(from_object, ['response_id']))

  if getv(from_object, ['model_version']) is not None:
    setv(to_object, ['modelVersion'], getv(from_object, ['model_version']))

  if getv(from_object, ['prompt_feedback']) is not None:
    setv(to_object, ['promptFeedback'], getv(from_object, ['prompt_feedback']))

  if getv(from_object, ['usage_metadata']) is not None:
    setv(to_object, ['usageMetadata'], getv(from_object, ['usage_metadata']))

  return to_object


def _JobError_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['details']) is not None:
    setv(to_object, ['details'], getv(from_object, ['details']))

  if getv(from_object, ['code']) is not None:
    setv(to_object, ['code'], getv(from_object, ['code']))

  if getv(from_object, ['message']) is not None:
    setv(to_object, ['message'], getv(from_object, ['message']))

  return to_object


def _InlinedResponse_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['response']) is not None:
    raise ValueError('response parameter is not supported in Vertex AI.')

  if getv(from_object, ['error']) is not None:
    raise ValueError('error parameter is not supported in Vertex AI.')

  return to_object


def _BatchJobDestination_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['format']) is not None:
    setv(to_object, ['predictionsFormat'], getv(from_object, ['format']))

  if getv(from_object, ['gcs_uri']) is not None:
    setv(
        to_object,
        ['gcsDestination', 'outputUriPrefix'],
        getv(from_object, ['gcs_uri']),
    )

  if getv(from_object, ['bigquery_uri']) is not None:
    setv(
        to_object,
        ['bigqueryDestination', 'outputUri'],
        getv(from_object, ['bigquery_uri']),
    )

  if getv(from_object, ['file_name']) is not None:
    raise ValueError('file_name parameter is not supported in Vertex AI.')

  if getv(from_object, ['inlined_responses']) is not None:
    raise ValueError(
        'inlined_responses parameter is not supported in Vertex AI.'
    )

  return to_object


def _CreateBatchJobConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['display_name']) is not None:
    setv(parent_object, ['displayName'], getv(from_object, ['display_name']))

  if getv(from_object, ['dest']) is not None:
    setv(
        parent_object,
        ['outputConfig'],
        _BatchJobDestination_to_vertex(
            t.t_batch_job_destination(getv(from_object, ['dest'])), to_object
        ),
    )

  return to_object


def _CreateBatchJobParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['model']) is not None:
    setv(
        to_object,
        ['model'],
        t.t_model(api_client, getv(from_object, ['model'])),
    )

  if getv(from_object, ['src']) is not None:
    setv(
        to_object,
        ['inputConfig'],
        _BatchJobSource_to_vertex(
            t.t_batch_job_source(api_client, getv(from_object, ['src'])),
            to_object,
        ),
    )

  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _CreateBatchJobConfig_to_vertex(
            getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _GetBatchJobParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _CancelBatchJobParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _ListBatchJobsConfig_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['page_size']) is not None:
    setv(
        parent_object, ['_query', 'pageSize'], getv(from_object, ['page_size'])
    )

  if getv(from_object, ['page_token']) is not None:
    setv(
        parent_object,
        ['_query', 'pageToken'],
        getv(from_object, ['page_token']),
    )

  if getv(from_object, ['filter']) is not None:
    setv(parent_object, ['_query', 'filter'], getv(from_object, ['filter']))

  return to_object


def _ListBatchJobsParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['config']) is not None:
    setv(
        to_object,
        ['config'],
        _ListBatchJobsConfig_to_vertex(
            getv(from_object, ['config']), to_object
        ),
    )

  return to_object


def _DeleteBatchJobParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(
        to_object,
        ['_url', 'name'],
        t.t_batch_job_name(api_client, getv(from_object, ['name'])),
    )

  if getv(from_object, ['config']) is not None:
    setv(to_object, ['config'], getv(from_object, ['config']))

  return to_object


def _FeatureSelectionPreference_to_mldev_enum_validate(enum_value: Any) -> None:
  if enum_value in set(
      [
          'FEATURE_SELECTION_PREFERENCE_UNSPECIFIED',
          'PRIORITIZE_QUALITY',
          'BALANCED',
          'PRIORITIZE_COST',
      ]
  ):
    raise ValueError(f'{enum_value} enum value is not supported in Gemini API.')


def _FinishReason_to_mldev_enum_validate(enum_value: Any) -> None:
  if enum_value in set(['UNEXPECTED_TOOL_CALL']):
    raise ValueError(f'{enum_value} enum value is not supported in Gemini API.')


def _Behavior_to_vertex_enum_validate(enum_value: Any) -> None:
  if enum_value in set(['UNSPECIFIED', 'BLOCKING', 'NON_BLOCKING']):
    raise ValueError(f'{enum_value} enum value is not supported in Vertex AI.')


def _FinishReason_to_vertex_enum_validate(enum_value: Any) -> None:
  if enum_value in set(['LANGUAGE', 'IMAGE_SAFETY']):
    raise ValueError(f'{enum_value} enum value is not supported in Vertex AI.')


def _JobError_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _VideoMetadata_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['endOffset']) is not None:
    setv(to_object, ['end_offset'], getv(from_object, ['endOffset']))

  if getv(from_object, ['startOffset']) is not None:
    setv(to_object, ['start_offset'], getv(from_object, ['startOffset']))

  return to_object


def _Blob_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mimeType']) is not None:
    setv(to_object, ['mime_type'], getv(from_object, ['mimeType']))

  return to_object


def _FileData_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['fileUri']) is not None:
    setv(to_object, ['file_uri'], getv(from_object, ['fileUri']))

  if getv(from_object, ['mimeType']) is not None:
    setv(to_object, ['mime_type'], getv(from_object, ['mimeType']))

  return to_object


def _Part_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['videoMetadata']) is not None:
    setv(
        to_object,
        ['video_metadata'],
        _VideoMetadata_from_mldev(
            getv(from_object, ['videoMetadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inlineData']) is not None:
    setv(
        to_object,
        ['inline_data'],
        _Blob_from_mldev(getv(from_object, ['inlineData']), to_object),
    )

  if getv(from_object, ['fileData']) is not None:
    setv(
        to_object,
        ['file_data'],
        _FileData_from_mldev(getv(from_object, ['fileData']), to_object),
    )

  if getv(from_object, ['thoughtSignature']) is not None:
    setv(
        to_object,
        ['thought_signature'],
        getv(from_object, ['thoughtSignature']),
    )

  if getv(from_object, ['codeExecutionResult']) is not None:
    setv(
        to_object,
        ['code_execution_result'],
        getv(from_object, ['codeExecutionResult']),
    )

  if getv(from_object, ['executableCode']) is not None:
    setv(to_object, ['executable_code'], getv(from_object, ['executableCode']))

  if getv(from_object, ['functionCall']) is not None:
    setv(to_object, ['function_call'], getv(from_object, ['functionCall']))

  if getv(from_object, ['functionResponse']) is not None:
    setv(
        to_object,
        ['function_response'],
        getv(from_object, ['functionResponse']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Content_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_from_mldev(item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _Schema_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['anyOf']) is not None:
    setv(to_object, ['any_of'], getv(from_object, ['anyOf']))

  if getv(from_object, ['default']) is not None:
    setv(to_object, ['default'], getv(from_object, ['default']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['enum']) is not None:
    setv(to_object, ['enum'], getv(from_object, ['enum']))

  if getv(from_object, ['example']) is not None:
    setv(to_object, ['example'], getv(from_object, ['example']))

  if getv(from_object, ['format']) is not None:
    setv(to_object, ['format'], getv(from_object, ['format']))

  if getv(from_object, ['items']) is not None:
    setv(to_object, ['items'], getv(from_object, ['items']))

  if getv(from_object, ['maxItems']) is not None:
    setv(to_object, ['max_items'], getv(from_object, ['maxItems']))

  if getv(from_object, ['maxLength']) is not None:
    setv(to_object, ['max_length'], getv(from_object, ['maxLength']))

  if getv(from_object, ['maxProperties']) is not None:
    setv(to_object, ['max_properties'], getv(from_object, ['maxProperties']))

  if getv(from_object, ['maximum']) is not None:
    setv(to_object, ['maximum'], getv(from_object, ['maximum']))

  if getv(from_object, ['minItems']) is not None:
    setv(to_object, ['min_items'], getv(from_object, ['minItems']))

  if getv(from_object, ['minLength']) is not None:
    setv(to_object, ['min_length'], getv(from_object, ['minLength']))

  if getv(from_object, ['minProperties']) is not None:
    setv(to_object, ['min_properties'], getv(from_object, ['minProperties']))

  if getv(from_object, ['minimum']) is not None:
    setv(to_object, ['minimum'], getv(from_object, ['minimum']))

  if getv(from_object, ['nullable']) is not None:
    setv(to_object, ['nullable'], getv(from_object, ['nullable']))

  if getv(from_object, ['pattern']) is not None:
    setv(to_object, ['pattern'], getv(from_object, ['pattern']))

  if getv(from_object, ['properties']) is not None:
    setv(to_object, ['properties'], getv(from_object, ['properties']))

  if getv(from_object, ['propertyOrdering']) is not None:
    setv(
        to_object,
        ['property_ordering'],
        getv(from_object, ['propertyOrdering']),
    )

  if getv(from_object, ['required']) is not None:
    setv(to_object, ['required'], getv(from_object, ['required']))

  if getv(from_object, ['title']) is not None:
    setv(to_object, ['title'], getv(from_object, ['title']))

  if getv(from_object, ['type']) is not None:
    setv(to_object, ['type'], getv(from_object, ['type']))

  return to_object


def _ModelSelectionConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _SafetySetting_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['category']) is not None:
    setv(to_object, ['category'], getv(from_object, ['category']))

  if getv(from_object, ['threshold']) is not None:
    setv(to_object, ['threshold'], getv(from_object, ['threshold']))

  return to_object


def _FunctionDeclaration_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['behavior']) is not None:
    setv(to_object, ['behavior'], getv(from_object, ['behavior']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['parametersJsonSchema']) is not None:
    setv(
        to_object,
        ['parameters_json_schema'],
        getv(from_object, ['parametersJsonSchema']),
    )

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  if getv(from_object, ['responseJsonSchema']) is not None:
    setv(
        to_object,
        ['response_json_schema'],
        getv(from_object, ['responseJsonSchema']),
    )

  return to_object


def _Interval_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['startTime']) is not None:
    setv(to_object, ['start_time'], getv(from_object, ['startTime']))

  if getv(from_object, ['endTime']) is not None:
    setv(to_object, ['end_time'], getv(from_object, ['endTime']))

  return to_object


def _GoogleSearch_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['timeRangeFilter']) is not None:
    setv(
        to_object,
        ['time_range_filter'],
        _Interval_from_mldev(getv(from_object, ['timeRangeFilter']), to_object),
    )

  return to_object


def _DynamicRetrievalConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamicThreshold']) is not None:
    setv(
        to_object,
        ['dynamic_threshold'],
        getv(from_object, ['dynamicThreshold']),
    )

  return to_object


def _GoogleSearchRetrieval_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamicRetrievalConfig']) is not None:
    setv(
        to_object,
        ['dynamic_retrieval_config'],
        _DynamicRetrievalConfig_from_mldev(
            getv(from_object, ['dynamicRetrievalConfig']), to_object
        ),
    )

  return to_object


def _EnterpriseWebSearch_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _ApiKeyConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _AuthConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['authType']) is not None:
    setv(to_object, ['auth_type'], getv(from_object, ['authType']))

  if getv(from_object, ['googleServiceAccountConfig']) is not None:
    setv(
        to_object,
        ['google_service_account_config'],
        getv(from_object, ['googleServiceAccountConfig']),
    )

  if getv(from_object, ['httpBasicAuthConfig']) is not None:
    setv(
        to_object,
        ['http_basic_auth_config'],
        getv(from_object, ['httpBasicAuthConfig']),
    )

  if getv(from_object, ['oauthConfig']) is not None:
    setv(to_object, ['oauth_config'], getv(from_object, ['oauthConfig']))

  if getv(from_object, ['oidcConfig']) is not None:
    setv(to_object, ['oidc_config'], getv(from_object, ['oidcConfig']))

  return to_object


def _GoogleMaps_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _UrlContext_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _Tool_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['functionDeclarations']) is not None:
    setv(
        to_object,
        ['function_declarations'],
        [
            _FunctionDeclaration_from_mldev(item, to_object)
            for item in getv(from_object, ['functionDeclarations'])
        ],
    )

  if getv(from_object, ['googleSearch']) is not None:
    setv(
        to_object,
        ['google_search'],
        _GoogleSearch_from_mldev(
            getv(from_object, ['googleSearch']), to_object
        ),
    )

  if getv(from_object, ['googleSearchRetrieval']) is not None:
    setv(
        to_object,
        ['google_search_retrieval'],
        _GoogleSearchRetrieval_from_mldev(
            getv(from_object, ['googleSearchRetrieval']), to_object
        ),
    )

  if getv(from_object, ['urlContext']) is not None:
    setv(
        to_object,
        ['url_context'],
        _UrlContext_from_mldev(getv(from_object, ['urlContext']), to_object),
    )

  if getv(from_object, ['codeExecution']) is not None:
    setv(to_object, ['code_execution'], getv(from_object, ['codeExecution']))

  if getv(from_object, ['computerUse']) is not None:
    setv(to_object, ['computer_use'], getv(from_object, ['computerUse']))

  return to_object


def _FunctionCallingConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['allowedFunctionNames']) is not None:
    setv(
        to_object,
        ['allowed_function_names'],
        getv(from_object, ['allowedFunctionNames']),
    )

  return to_object


def _LatLng_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latitude']) is not None:
    setv(to_object, ['latitude'], getv(from_object, ['latitude']))

  if getv(from_object, ['longitude']) is not None:
    setv(to_object, ['longitude'], getv(from_object, ['longitude']))

  return to_object


def _RetrievalConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latLng']) is not None:
    setv(
        to_object,
        ['lat_lng'],
        _LatLng_from_mldev(getv(from_object, ['latLng']), to_object),
    )

  if getv(from_object, ['languageCode']) is not None:
    setv(to_object, ['language_code'], getv(from_object, ['languageCode']))

  return to_object


def _ToolConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['functionCallingConfig']) is not None:
    setv(
        to_object,
        ['function_calling_config'],
        _FunctionCallingConfig_from_mldev(
            getv(from_object, ['functionCallingConfig']), to_object
        ),
    )

  if getv(from_object, ['retrievalConfig']) is not None:
    setv(
        to_object,
        ['retrieval_config'],
        _RetrievalConfig_from_mldev(
            getv(from_object, ['retrievalConfig']), to_object
        ),
    )

  return to_object


def _PrebuiltVoiceConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voiceName']) is not None:
    setv(to_object, ['voice_name'], getv(from_object, ['voiceName']))

  return to_object


def _VoiceConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuiltVoiceConfig']) is not None:
    setv(
        to_object,
        ['prebuilt_voice_config'],
        _PrebuiltVoiceConfig_from_mldev(
            getv(from_object, ['prebuiltVoiceConfig']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speaker']) is not None:
    setv(to_object, ['speaker'], getv(from_object, ['speaker']))

  if getv(from_object, ['voiceConfig']) is not None:
    setv(
        to_object,
        ['voice_config'],
        _VoiceConfig_from_mldev(getv(from_object, ['voiceConfig']), to_object),
    )

  return to_object


def _MultiSpeakerVoiceConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['speakerVoiceConfigs']) is not None:
    setv(
        to_object,
        ['speaker_voice_configs'],
        [
            _SpeakerVoiceConfig_from_mldev(item, to_object)
            for item in getv(from_object, ['speakerVoiceConfigs'])
        ],
    )

  return to_object


def _SpeechConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voiceConfig']) is not None:
    setv(
        to_object,
        ['voice_config'],
        _VoiceConfig_from_mldev(getv(from_object, ['voiceConfig']), to_object),
    )

  if getv(from_object, ['multiSpeakerVoiceConfig']) is not None:
    setv(
        to_object,
        ['multi_speaker_voice_config'],
        _MultiSpeakerVoiceConfig_from_mldev(
            getv(from_object, ['multiSpeakerVoiceConfig']), to_object
        ),
    )

  if getv(from_object, ['languageCode']) is not None:
    setv(to_object, ['language_code'], getv(from_object, ['languageCode']))

  return to_object


def _ThinkingConfig_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['includeThoughts']) is not None:
    setv(
        to_object, ['include_thoughts'], getv(from_object, ['includeThoughts'])
    )

  if getv(from_object, ['thinkingBudget']) is not None:
    setv(to_object, ['thinking_budget'], getv(from_object, ['thinkingBudget']))

  return to_object


def _GenerateContentConfig_from_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(parent_object, ['systemInstruction']) is not None:
    setv(
        to_object,
        ['system_instruction'],
        _Content_from_mldev(
            t.t_content(getv(parent_object, ['systemInstruction'])), to_object
        ),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(to_object, ['temperature'], getv(from_object, ['temperature']))

  if getv(from_object, ['topP']) is not None:
    setv(to_object, ['top_p'], getv(from_object, ['topP']))

  if getv(from_object, ['topK']) is not None:
    setv(to_object, ['top_k'], getv(from_object, ['topK']))

  if getv(from_object, ['candidateCount']) is not None:
    setv(to_object, ['candidate_count'], getv(from_object, ['candidateCount']))

  if getv(from_object, ['maxOutputTokens']) is not None:
    setv(
        to_object, ['max_output_tokens'], getv(from_object, ['maxOutputTokens'])
    )

  if getv(from_object, ['stopSequences']) is not None:
    setv(to_object, ['stop_sequences'], getv(from_object, ['stopSequences']))

  if getv(from_object, ['responseLogprobs']) is not None:
    setv(
        to_object,
        ['response_logprobs'],
        getv(from_object, ['responseLogprobs']),
    )

  if getv(from_object, ['logprobs']) is not None:
    setv(to_object, ['logprobs'], getv(from_object, ['logprobs']))

  if getv(from_object, ['presencePenalty']) is not None:
    setv(
        to_object, ['presence_penalty'], getv(from_object, ['presencePenalty'])
    )

  if getv(from_object, ['frequencyPenalty']) is not None:
    setv(
        to_object,
        ['frequency_penalty'],
        getv(from_object, ['frequencyPenalty']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(to_object, ['seed'], getv(from_object, ['seed']))

  if getv(from_object, ['responseMimeType']) is not None:
    setv(
        to_object,
        ['response_mime_type'],
        getv(from_object, ['responseMimeType']),
    )

  if getv(from_object, ['responseSchema']) is not None:
    setv(
        to_object,
        ['response_schema'],
        _Schema_from_mldev(
            t.t_schema(api_client, getv(from_object, ['responseSchema'])),
            to_object,
        ),
    )

  if getv(from_object, ['responseJsonSchema']) is not None:
    setv(
        to_object,
        ['response_json_schema'],
        getv(from_object, ['responseJsonSchema']),
    )

  if getv(parent_object, ['safetySettings']) is not None:
    setv(
        to_object,
        ['safety_settings'],
        [
            _SafetySetting_from_mldev(item, to_object)
            for item in getv(parent_object, ['safetySettings'])
        ],
    )

  if getv(parent_object, ['tools']) is not None:
    setv(
        to_object,
        ['tools'],
        [
            _Tool_from_mldev(t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(parent_object, ['tools']))
        ],
    )

  if getv(parent_object, ['toolConfig']) is not None:
    setv(
        to_object,
        ['tool_config'],
        _ToolConfig_from_mldev(getv(parent_object, ['toolConfig']), to_object),
    )

  if getv(parent_object, ['cachedContent']) is not None:
    setv(
        to_object,
        ['cached_content'],
        t.t_cached_content_name(
            api_client, getv(parent_object, ['cachedContent'])
        ),
    )

  if getv(from_object, ['responseModalities']) is not None:
    setv(
        to_object,
        ['response_modalities'],
        getv(from_object, ['responseModalities']),
    )

  if getv(from_object, ['mediaResolution']) is not None:
    setv(
        to_object, ['media_resolution'], getv(from_object, ['mediaResolution'])
    )

  if getv(from_object, ['speechConfig']) is not None:
    setv(
        to_object,
        ['speech_config'],
        _SpeechConfig_from_mldev(
            t.t_speech_config(getv(from_object, ['speechConfig'])), to_object
        ),
    )

  if getv(from_object, ['thinkingConfig']) is not None:
    setv(
        to_object,
        ['thinking_config'],
        _ThinkingConfig_from_mldev(
            getv(from_object, ['thinkingConfig']), to_object
        ),
    )

  return to_object


def _InlinedRequest_from_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['request', 'model']) is not None:
    setv(
        to_object,
        ['model'],
        t.t_model(api_client, getv(from_object, ['request', 'model'])),
    )

  if getv(from_object, ['request', 'contents']) is not None:
    setv(
        to_object,
        ['contents'],
        [
            _Content_from_mldev(item, to_object)
            for item in t.t_contents(getv(from_object, ['request', 'contents']))
        ],
    )

  if getv(from_object, ['request', 'generationConfig']) is not None:
    setv(
        to_object,
        ['config'],
        _GenerateContentConfig_from_mldev(
            api_client,
            getv(from_object, ['request', 'generationConfig']),
            to_object,
        ),
    )

  return to_object


def _BatchJobSource_from_mldev(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['fileName']) is not None:
    setv(to_object, ['file_name'], getv(from_object, ['fileName']))

  if getv(from_object, ['requests', 'requests']) is not None:
    setv(
        to_object,
        ['inlined_requests'],
        [
            _InlinedRequest_from_mldev(api_client, item, to_object)
            for item in getv(from_object, ['requests', 'requests'])
        ],
    )

  return to_object


def _CitationMetadata_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['citationSources']) is not None:
    setv(to_object, ['citations'], getv(from_object, ['citationSources']))

  return to_object


def _UrlMetadata_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['retrievedUrl']) is not None:
    setv(to_object, ['retrieved_url'], getv(from_object, ['retrievedUrl']))

  if getv(from_object, ['urlRetrievalStatus']) is not None:
    setv(
        to_object,
        ['url_retrieval_status'],
        getv(from_object, ['urlRetrievalStatus']),
    )

  return to_object


def _UrlContextMetadata_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['urlMetadata']) is not None:
    setv(
        to_object,
        ['url_metadata'],
        [
            _UrlMetadata_from_mldev(item, to_object)
            for item in getv(from_object, ['urlMetadata'])
        ],
    )

  return to_object


def _Candidate_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['content']) is not None:
    setv(
        to_object,
        ['content'],
        _Content_from_mldev(getv(from_object, ['content']), to_object),
    )

  if getv(from_object, ['citationMetadata']) is not None:
    setv(
        to_object,
        ['citation_metadata'],
        _CitationMetadata_from_mldev(
            getv(from_object, ['citationMetadata']), to_object
        ),
    )

  if getv(from_object, ['tokenCount']) is not None:
    setv(to_object, ['token_count'], getv(from_object, ['tokenCount']))

  if getv(from_object, ['finishReason']) is not None:
    setv(to_object, ['finish_reason'], getv(from_object, ['finishReason']))

  if getv(from_object, ['urlContextMetadata']) is not None:
    setv(
        to_object,
        ['url_context_metadata'],
        _UrlContextMetadata_from_mldev(
            getv(from_object, ['urlContextMetadata']), to_object
        ),
    )

  if getv(from_object, ['avgLogprobs']) is not None:
    setv(to_object, ['avg_logprobs'], getv(from_object, ['avgLogprobs']))

  if getv(from_object, ['groundingMetadata']) is not None:
    setv(
        to_object,
        ['grounding_metadata'],
        getv(from_object, ['groundingMetadata']),
    )

  if getv(from_object, ['index']) is not None:
    setv(to_object, ['index'], getv(from_object, ['index']))

  if getv(from_object, ['logprobsResult']) is not None:
    setv(to_object, ['logprobs_result'], getv(from_object, ['logprobsResult']))

  if getv(from_object, ['safetyRatings']) is not None:
    setv(to_object, ['safety_ratings'], getv(from_object, ['safetyRatings']))

  return to_object


def _GenerateContentResponse_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['sdkHttpResponse']) is not None:
    setv(
        to_object, ['sdk_http_response'], getv(from_object, ['sdkHttpResponse'])
    )

  if getv(from_object, ['candidates']) is not None:
    setv(
        to_object,
        ['candidates'],
        [
            _Candidate_from_mldev(item, to_object)
            for item in getv(from_object, ['candidates'])
        ],
    )

  if getv(from_object, ['modelVersion']) is not None:
    setv(to_object, ['model_version'], getv(from_object, ['modelVersion']))

  if getv(from_object, ['promptFeedback']) is not None:
    setv(to_object, ['prompt_feedback'], getv(from_object, ['promptFeedback']))

  if getv(from_object, ['usageMetadata']) is not None:
    setv(to_object, ['usage_metadata'], getv(from_object, ['usageMetadata']))

  return to_object


def _InlinedResponse_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['response']) is not None:
    setv(
        to_object,
        ['response'],
        _GenerateContentResponse_from_mldev(
            getv(from_object, ['response']), to_object
        ),
    )

  if getv(from_object, ['error']) is not None:
    setv(
        to_object,
        ['error'],
        _JobError_from_mldev(getv(from_object, ['error']), to_object),
    )

  return to_object


def _BatchJobDestination_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['responsesFile']) is not None:
    setv(to_object, ['file_name'], getv(from_object, ['responsesFile']))

  if getv(from_object, ['inlinedResponses', 'inlinedResponses']) is not None:
    setv(
        to_object,
        ['inlined_responses'],
        [
            _InlinedResponse_from_mldev(item, to_object)
            for item in getv(
                from_object, ['inlinedResponses', 'inlinedResponses']
            )
        ],
    )

  return to_object


def _BatchJob_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['metadata', 'displayName']) is not None:
    setv(
        to_object,
        ['display_name'],
        getv(from_object, ['metadata', 'displayName']),
    )

  if getv(from_object, ['metadata', 'state']) is not None:
    setv(
        to_object,
        ['state'],
        t.t_job_state(getv(from_object, ['metadata', 'state'])),
    )

  if getv(from_object, ['metadata', 'createTime']) is not None:
    setv(
        to_object,
        ['create_time'],
        getv(from_object, ['metadata', 'createTime']),
    )

  if getv(from_object, ['metadata', 'endTime']) is not None:
    setv(to_object, ['end_time'], getv(from_object, ['metadata', 'endTime']))

  if getv(from_object, ['metadata', 'updateTime']) is not None:
    setv(
        to_object,
        ['update_time'],
        getv(from_object, ['metadata', 'updateTime']),
    )

  if getv(from_object, ['metadata', 'model']) is not None:
    setv(to_object, ['model'], getv(from_object, ['metadata', 'model']))

  if getv(from_object, ['metadata', 'output']) is not None:
    setv(
        to_object,
        ['dest'],
        _BatchJobDestination_from_mldev(
            getv(from_object, ['metadata', 'output']), to_object
        ),
    )

  return to_object


def _ListBatchJobsResponse_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['nextPageToken']) is not None:
    setv(to_object, ['next_page_token'], getv(from_object, ['nextPageToken']))

  if getv(from_object, ['operations']) is not None:
    setv(
        to_object,
        ['batch_jobs'],
        [
            _BatchJob_from_mldev(item, to_object)
            for item in getv(from_object, ['operations'])
        ],
    )

  return to_object


def _DeleteResourceJob_from_mldev(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['done']) is not None:
    setv(to_object, ['done'], getv(from_object, ['done']))

  if getv(from_object, ['error']) is not None:
    setv(
        to_object,
        ['error'],
        _JobError_from_mldev(getv(from_object, ['error']), to_object),
    )

  return to_object


def _JobError_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['details']) is not None:
    setv(to_object, ['details'], getv(from_object, ['details']))

  if getv(from_object, ['code']) is not None:
    setv(to_object, ['code'], getv(from_object, ['code']))

  if getv(from_object, ['message']) is not None:
    setv(to_object, ['message'], getv(from_object, ['message']))

  return to_object


def _VideoMetadata_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['fps']) is not None:
    setv(to_object, ['fps'], getv(from_object, ['fps']))

  if getv(from_object, ['endOffset']) is not None:
    setv(to_object, ['end_offset'], getv(from_object, ['endOffset']))

  if getv(from_object, ['startOffset']) is not None:
    setv(to_object, ['start_offset'], getv(from_object, ['startOffset']))

  return to_object


def _Blob_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['displayName']) is not None:
    setv(to_object, ['display_name'], getv(from_object, ['displayName']))

  if getv(from_object, ['data']) is not None:
    setv(to_object, ['data'], getv(from_object, ['data']))

  if getv(from_object, ['mimeType']) is not None:
    setv(to_object, ['mime_type'], getv(from_object, ['mimeType']))

  return to_object


def _FileData_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['displayName']) is not None:
    setv(to_object, ['display_name'], getv(from_object, ['displayName']))

  if getv(from_object, ['fileUri']) is not None:
    setv(to_object, ['file_uri'], getv(from_object, ['fileUri']))

  if getv(from_object, ['mimeType']) is not None:
    setv(to_object, ['mime_type'], getv(from_object, ['mimeType']))

  return to_object


def _Part_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['videoMetadata']) is not None:
    setv(
        to_object,
        ['video_metadata'],
        _VideoMetadata_from_vertex(
            getv(from_object, ['videoMetadata']), to_object
        ),
    )

  if getv(from_object, ['thought']) is not None:
    setv(to_object, ['thought'], getv(from_object, ['thought']))

  if getv(from_object, ['inlineData']) is not None:
    setv(
        to_object,
        ['inline_data'],
        _Blob_from_vertex(getv(from_object, ['inlineData']), to_object),
    )

  if getv(from_object, ['fileData']) is not None:
    setv(
        to_object,
        ['file_data'],
        _FileData_from_vertex(getv(from_object, ['fileData']), to_object),
    )

  if getv(from_object, ['thoughtSignature']) is not None:
    setv(
        to_object,
        ['thought_signature'],
        getv(from_object, ['thoughtSignature']),
    )

  if getv(from_object, ['codeExecutionResult']) is not None:
    setv(
        to_object,
        ['code_execution_result'],
        getv(from_object, ['codeExecutionResult']),
    )

  if getv(from_object, ['executableCode']) is not None:
    setv(to_object, ['executable_code'], getv(from_object, ['executableCode']))

  if getv(from_object, ['functionCall']) is not None:
    setv(to_object, ['function_call'], getv(from_object, ['functionCall']))

  if getv(from_object, ['functionResponse']) is not None:
    setv(
        to_object,
        ['function_response'],
        getv(from_object, ['functionResponse']),
    )

  if getv(from_object, ['text']) is not None:
    setv(to_object, ['text'], getv(from_object, ['text']))

  return to_object


def _Content_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['parts']) is not None:
    setv(
        to_object,
        ['parts'],
        [
            _Part_from_vertex(item, to_object)
            for item in getv(from_object, ['parts'])
        ],
    )

  if getv(from_object, ['role']) is not None:
    setv(to_object, ['role'], getv(from_object, ['role']))

  return to_object


def _Schema_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['additionalProperties']) is not None:
    setv(
        to_object,
        ['additional_properties'],
        getv(from_object, ['additionalProperties']),
    )

  if getv(from_object, ['defs']) is not None:
    setv(to_object, ['defs'], getv(from_object, ['defs']))

  if getv(from_object, ['ref']) is not None:
    setv(to_object, ['ref'], getv(from_object, ['ref']))

  if getv(from_object, ['anyOf']) is not None:
    setv(to_object, ['any_of'], getv(from_object, ['anyOf']))

  if getv(from_object, ['default']) is not None:
    setv(to_object, ['default'], getv(from_object, ['default']))

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['enum']) is not None:
    setv(to_object, ['enum'], getv(from_object, ['enum']))

  if getv(from_object, ['example']) is not None:
    setv(to_object, ['example'], getv(from_object, ['example']))

  if getv(from_object, ['format']) is not None:
    setv(to_object, ['format'], getv(from_object, ['format']))

  if getv(from_object, ['items']) is not None:
    setv(to_object, ['items'], getv(from_object, ['items']))

  if getv(from_object, ['maxItems']) is not None:
    setv(to_object, ['max_items'], getv(from_object, ['maxItems']))

  if getv(from_object, ['maxLength']) is not None:
    setv(to_object, ['max_length'], getv(from_object, ['maxLength']))

  if getv(from_object, ['maxProperties']) is not None:
    setv(to_object, ['max_properties'], getv(from_object, ['maxProperties']))

  if getv(from_object, ['maximum']) is not None:
    setv(to_object, ['maximum'], getv(from_object, ['maximum']))

  if getv(from_object, ['minItems']) is not None:
    setv(to_object, ['min_items'], getv(from_object, ['minItems']))

  if getv(from_object, ['minLength']) is not None:
    setv(to_object, ['min_length'], getv(from_object, ['minLength']))

  if getv(from_object, ['minProperties']) is not None:
    setv(to_object, ['min_properties'], getv(from_object, ['minProperties']))

  if getv(from_object, ['minimum']) is not None:
    setv(to_object, ['minimum'], getv(from_object, ['minimum']))

  if getv(from_object, ['nullable']) is not None:
    setv(to_object, ['nullable'], getv(from_object, ['nullable']))

  if getv(from_object, ['pattern']) is not None:
    setv(to_object, ['pattern'], getv(from_object, ['pattern']))

  if getv(from_object, ['properties']) is not None:
    setv(to_object, ['properties'], getv(from_object, ['properties']))

  if getv(from_object, ['propertyOrdering']) is not None:
    setv(
        to_object,
        ['property_ordering'],
        getv(from_object, ['propertyOrdering']),
    )

  if getv(from_object, ['required']) is not None:
    setv(to_object, ['required'], getv(from_object, ['required']))

  if getv(from_object, ['title']) is not None:
    setv(to_object, ['title'], getv(from_object, ['title']))

  if getv(from_object, ['type']) is not None:
    setv(to_object, ['type'], getv(from_object, ['type']))

  return to_object


def _ModelSelectionConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['featureSelectionPreference']) is not None:
    setv(
        to_object,
        ['feature_selection_preference'],
        getv(from_object, ['featureSelectionPreference']),
    )

  return to_object


def _SafetySetting_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['method']) is not None:
    setv(to_object, ['method'], getv(from_object, ['method']))

  if getv(from_object, ['category']) is not None:
    setv(to_object, ['category'], getv(from_object, ['category']))

  if getv(from_object, ['threshold']) is not None:
    setv(to_object, ['threshold'], getv(from_object, ['threshold']))

  return to_object


def _FunctionDeclaration_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['description']) is not None:
    setv(to_object, ['description'], getv(from_object, ['description']))

  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['parameters']) is not None:
    setv(to_object, ['parameters'], getv(from_object, ['parameters']))

  if getv(from_object, ['parametersJsonSchema']) is not None:
    setv(
        to_object,
        ['parameters_json_schema'],
        getv(from_object, ['parametersJsonSchema']),
    )

  if getv(from_object, ['response']) is not None:
    setv(to_object, ['response'], getv(from_object, ['response']))

  if getv(from_object, ['responseJsonSchema']) is not None:
    setv(
        to_object,
        ['response_json_schema'],
        getv(from_object, ['responseJsonSchema']),
    )

  return to_object


def _Interval_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['startTime']) is not None:
    setv(to_object, ['start_time'], getv(from_object, ['startTime']))

  if getv(from_object, ['endTime']) is not None:
    setv(to_object, ['end_time'], getv(from_object, ['endTime']))

  return to_object


def _GoogleSearch_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['timeRangeFilter']) is not None:
    setv(
        to_object,
        ['time_range_filter'],
        _Interval_from_vertex(
            getv(from_object, ['timeRangeFilter']), to_object
        ),
    )

  return to_object


def _DynamicRetrievalConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['dynamicThreshold']) is not None:
    setv(
        to_object,
        ['dynamic_threshold'],
        getv(from_object, ['dynamicThreshold']),
    )

  return to_object


def _GoogleSearchRetrieval_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['dynamicRetrievalConfig']) is not None:
    setv(
        to_object,
        ['dynamic_retrieval_config'],
        _DynamicRetrievalConfig_from_vertex(
            getv(from_object, ['dynamicRetrievalConfig']), to_object
        ),
    )

  return to_object


def _EnterpriseWebSearch_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _ApiKeyConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['apiKeyString']) is not None:
    setv(to_object, ['api_key_string'], getv(from_object, ['apiKeyString']))

  return to_object


def _AuthConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['apiKeyConfig']) is not None:
    setv(
        to_object,
        ['api_key_config'],
        _ApiKeyConfig_from_vertex(
            getv(from_object, ['apiKeyConfig']), to_object
        ),
    )

  if getv(from_object, ['authType']) is not None:
    setv(to_object, ['auth_type'], getv(from_object, ['authType']))

  if getv(from_object, ['googleServiceAccountConfig']) is not None:
    setv(
        to_object,
        ['google_service_account_config'],
        getv(from_object, ['googleServiceAccountConfig']),
    )

  if getv(from_object, ['httpBasicAuthConfig']) is not None:
    setv(
        to_object,
        ['http_basic_auth_config'],
        getv(from_object, ['httpBasicAuthConfig']),
    )

  if getv(from_object, ['oauthConfig']) is not None:
    setv(to_object, ['oauth_config'], getv(from_object, ['oauthConfig']))

  if getv(from_object, ['oidcConfig']) is not None:
    setv(to_object, ['oidc_config'], getv(from_object, ['oidcConfig']))

  return to_object


def _GoogleMaps_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['authConfig']) is not None:
    setv(
        to_object,
        ['auth_config'],
        _AuthConfig_from_vertex(getv(from_object, ['authConfig']), to_object),
    )

  return to_object


def _UrlContext_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _Tool_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['functionDeclarations']) is not None:
    setv(
        to_object,
        ['function_declarations'],
        [
            _FunctionDeclaration_from_vertex(item, to_object)
            for item in getv(from_object, ['functionDeclarations'])
        ],
    )

  if getv(from_object, ['retrieval']) is not None:
    setv(to_object, ['retrieval'], getv(from_object, ['retrieval']))

  if getv(from_object, ['googleSearch']) is not None:
    setv(
        to_object,
        ['google_search'],
        _GoogleSearch_from_vertex(
            getv(from_object, ['googleSearch']), to_object
        ),
    )

  if getv(from_object, ['googleSearchRetrieval']) is not None:
    setv(
        to_object,
        ['google_search_retrieval'],
        _GoogleSearchRetrieval_from_vertex(
            getv(from_object, ['googleSearchRetrieval']), to_object
        ),
    )

  if getv(from_object, ['enterpriseWebSearch']) is not None:
    setv(
        to_object,
        ['enterprise_web_search'],
        _EnterpriseWebSearch_from_vertex(
            getv(from_object, ['enterpriseWebSearch']), to_object
        ),
    )

  if getv(from_object, ['googleMaps']) is not None:
    setv(
        to_object,
        ['google_maps'],
        _GoogleMaps_from_vertex(getv(from_object, ['googleMaps']), to_object),
    )

  if getv(from_object, ['urlContext']) is not None:
    setv(
        to_object,
        ['url_context'],
        _UrlContext_from_vertex(getv(from_object, ['urlContext']), to_object),
    )

  if getv(from_object, ['codeExecution']) is not None:
    setv(to_object, ['code_execution'], getv(from_object, ['codeExecution']))

  if getv(from_object, ['computerUse']) is not None:
    setv(to_object, ['computer_use'], getv(from_object, ['computerUse']))

  return to_object


def _FunctionCallingConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['mode']) is not None:
    setv(to_object, ['mode'], getv(from_object, ['mode']))

  if getv(from_object, ['allowedFunctionNames']) is not None:
    setv(
        to_object,
        ['allowed_function_names'],
        getv(from_object, ['allowedFunctionNames']),
    )

  return to_object


def _LatLng_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latitude']) is not None:
    setv(to_object, ['latitude'], getv(from_object, ['latitude']))

  if getv(from_object, ['longitude']) is not None:
    setv(to_object, ['longitude'], getv(from_object, ['longitude']))

  return to_object


def _RetrievalConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['latLng']) is not None:
    setv(
        to_object,
        ['lat_lng'],
        _LatLng_from_vertex(getv(from_object, ['latLng']), to_object),
    )

  if getv(from_object, ['languageCode']) is not None:
    setv(to_object, ['language_code'], getv(from_object, ['languageCode']))

  return to_object


def _ToolConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['functionCallingConfig']) is not None:
    setv(
        to_object,
        ['function_calling_config'],
        _FunctionCallingConfig_from_vertex(
            getv(from_object, ['functionCallingConfig']), to_object
        ),
    )

  if getv(from_object, ['retrievalConfig']) is not None:
    setv(
        to_object,
        ['retrieval_config'],
        _RetrievalConfig_from_vertex(
            getv(from_object, ['retrievalConfig']), to_object
        ),
    )

  return to_object


def _PrebuiltVoiceConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voiceName']) is not None:
    setv(to_object, ['voice_name'], getv(from_object, ['voiceName']))

  return to_object


def _VoiceConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['prebuiltVoiceConfig']) is not None:
    setv(
        to_object,
        ['prebuilt_voice_config'],
        _PrebuiltVoiceConfig_from_vertex(
            getv(from_object, ['prebuiltVoiceConfig']), to_object
        ),
    )

  return to_object


def _SpeakerVoiceConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _MultiSpeakerVoiceConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _SpeechConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['voiceConfig']) is not None:
    setv(
        to_object,
        ['voice_config'],
        _VoiceConfig_from_vertex(getv(from_object, ['voiceConfig']), to_object),
    )

  if getv(from_object, ['languageCode']) is not None:
    setv(to_object, ['language_code'], getv(from_object, ['languageCode']))

  return to_object


def _ThinkingConfig_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['includeThoughts']) is not None:
    setv(
        to_object, ['include_thoughts'], getv(from_object, ['includeThoughts'])
    )

  if getv(from_object, ['thinkingBudget']) is not None:
    setv(to_object, ['thinking_budget'], getv(from_object, ['thinkingBudget']))

  return to_object


def _GenerateContentConfig_from_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(parent_object, ['systemInstruction']) is not None:
    setv(
        to_object,
        ['system_instruction'],
        _Content_from_vertex(
            t.t_content(getv(parent_object, ['systemInstruction'])), to_object
        ),
    )

  if getv(from_object, ['temperature']) is not None:
    setv(to_object, ['temperature'], getv(from_object, ['temperature']))

  if getv(from_object, ['topP']) is not None:
    setv(to_object, ['top_p'], getv(from_object, ['topP']))

  if getv(from_object, ['topK']) is not None:
    setv(to_object, ['top_k'], getv(from_object, ['topK']))

  if getv(from_object, ['candidateCount']) is not None:
    setv(to_object, ['candidate_count'], getv(from_object, ['candidateCount']))

  if getv(from_object, ['maxOutputTokens']) is not None:
    setv(
        to_object, ['max_output_tokens'], getv(from_object, ['maxOutputTokens'])
    )

  if getv(from_object, ['stopSequences']) is not None:
    setv(to_object, ['stop_sequences'], getv(from_object, ['stopSequences']))

  if getv(from_object, ['responseLogprobs']) is not None:
    setv(
        to_object,
        ['response_logprobs'],
        getv(from_object, ['responseLogprobs']),
    )

  if getv(from_object, ['logprobs']) is not None:
    setv(to_object, ['logprobs'], getv(from_object, ['logprobs']))

  if getv(from_object, ['presencePenalty']) is not None:
    setv(
        to_object, ['presence_penalty'], getv(from_object, ['presencePenalty'])
    )

  if getv(from_object, ['frequencyPenalty']) is not None:
    setv(
        to_object,
        ['frequency_penalty'],
        getv(from_object, ['frequencyPenalty']),
    )

  if getv(from_object, ['seed']) is not None:
    setv(to_object, ['seed'], getv(from_object, ['seed']))

  if getv(from_object, ['responseMimeType']) is not None:
    setv(
        to_object,
        ['response_mime_type'],
        getv(from_object, ['responseMimeType']),
    )

  if getv(from_object, ['responseSchema']) is not None:
    setv(
        to_object,
        ['response_schema'],
        _Schema_from_vertex(
            t.t_schema(api_client, getv(from_object, ['responseSchema'])),
            to_object,
        ),
    )

  if getv(from_object, ['responseJsonSchema']) is not None:
    setv(
        to_object,
        ['response_json_schema'],
        getv(from_object, ['responseJsonSchema']),
    )

  if getv(from_object, ['routingConfig']) is not None:
    setv(to_object, ['routing_config'], getv(from_object, ['routingConfig']))

  if getv(from_object, ['modelConfig']) is not None:
    setv(
        to_object,
        ['model_selection_config'],
        _ModelSelectionConfig_from_vertex(
            getv(from_object, ['modelConfig']), to_object
        ),
    )

  if getv(parent_object, ['safetySettings']) is not None:
    setv(
        to_object,
        ['safety_settings'],
        [
            _SafetySetting_from_vertex(item, to_object)
            for item in getv(parent_object, ['safetySettings'])
        ],
    )

  if getv(parent_object, ['tools']) is not None:
    setv(
        to_object,
        ['tools'],
        [
            _Tool_from_vertex(t.t_tool(api_client, item), to_object)
            for item in t.t_tools(api_client, getv(parent_object, ['tools']))
        ],
    )

  if getv(parent_object, ['toolConfig']) is not None:
    setv(
        to_object,
        ['tool_config'],
        _ToolConfig_from_vertex(getv(parent_object, ['toolConfig']), to_object),
    )

  if getv(parent_object, ['labels']) is not None:
    setv(to_object, ['labels'], getv(parent_object, ['labels']))

  if getv(parent_object, ['cachedContent']) is not None:
    setv(
        to_object,
        ['cached_content'],
        t.t_cached_content_name(
            api_client, getv(parent_object, ['cachedContent'])
        ),
    )

  if getv(from_object, ['responseModalities']) is not None:
    setv(
        to_object,
        ['response_modalities'],
        getv(from_object, ['responseModalities']),
    )

  if getv(from_object, ['mediaResolution']) is not None:
    setv(
        to_object, ['media_resolution'], getv(from_object, ['mediaResolution'])
    )

  if getv(from_object, ['speechConfig']) is not None:
    setv(
        to_object,
        ['speech_config'],
        _SpeechConfig_from_vertex(
            t.t_speech_config(getv(from_object, ['speechConfig'])), to_object
        ),
    )

  if getv(from_object, ['audioTimestamp']) is not None:
    setv(to_object, ['audio_timestamp'], getv(from_object, ['audioTimestamp']))

  if getv(from_object, ['thinkingConfig']) is not None:
    setv(
        to_object,
        ['thinking_config'],
        _ThinkingConfig_from_vertex(
            getv(from_object, ['thinkingConfig']), to_object
        ),
    )

  return to_object


def _InlinedRequest_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _BatchJobSource_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['instancesFormat']) is not None:
    setv(to_object, ['format'], getv(from_object, ['instancesFormat']))

  if getv(from_object, ['gcsSource', 'uris']) is not None:
    setv(to_object, ['gcs_uri'], getv(from_object, ['gcsSource', 'uris']))

  if getv(from_object, ['bigquerySource', 'inputUri']) is not None:
    setv(
        to_object,
        ['bigquery_uri'],
        getv(from_object, ['bigquerySource', 'inputUri']),
    )

  return to_object


def _CitationMetadata_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['citations']) is not None:
    setv(to_object, ['citations'], getv(from_object, ['citations']))

  return to_object


def _UrlMetadata_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['retrievedUrl']) is not None:
    setv(to_object, ['retrieved_url'], getv(from_object, ['retrievedUrl']))

  if getv(from_object, ['urlRetrievalStatus']) is not None:
    setv(
        to_object,
        ['url_retrieval_status'],
        getv(from_object, ['urlRetrievalStatus']),
    )

  return to_object


def _UrlContextMetadata_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['urlMetadata']) is not None:
    setv(
        to_object,
        ['url_metadata'],
        [
            _UrlMetadata_from_vertex(item, to_object)
            for item in getv(from_object, ['urlMetadata'])
        ],
    )

  return to_object


def _Candidate_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['content']) is not None:
    setv(
        to_object,
        ['content'],
        _Content_from_vertex(getv(from_object, ['content']), to_object),
    )

  if getv(from_object, ['citationMetadata']) is not None:
    setv(
        to_object,
        ['citation_metadata'],
        _CitationMetadata_from_vertex(
            getv(from_object, ['citationMetadata']), to_object
        ),
    )

  if getv(from_object, ['finishMessage']) is not None:
    setv(to_object, ['finish_message'], getv(from_object, ['finishMessage']))

  if getv(from_object, ['finishReason']) is not None:
    setv(to_object, ['finish_reason'], getv(from_object, ['finishReason']))

  if getv(from_object, ['urlContextMetadata']) is not None:
    setv(
        to_object,
        ['url_context_metadata'],
        _UrlContextMetadata_from_vertex(
            getv(from_object, ['urlContextMetadata']), to_object
        ),
    )

  if getv(from_object, ['avgLogprobs']) is not None:
    setv(to_object, ['avg_logprobs'], getv(from_object, ['avgLogprobs']))

  if getv(from_object, ['groundingMetadata']) is not None:
    setv(
        to_object,
        ['grounding_metadata'],
        getv(from_object, ['groundingMetadata']),
    )

  if getv(from_object, ['index']) is not None:
    setv(to_object, ['index'], getv(from_object, ['index']))

  if getv(from_object, ['logprobsResult']) is not None:
    setv(to_object, ['logprobs_result'], getv(from_object, ['logprobsResult']))

  if getv(from_object, ['safetyRatings']) is not None:
    setv(to_object, ['safety_ratings'], getv(from_object, ['safetyRatings']))

  return to_object


def _GenerateContentResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  if getv(from_object, ['sdkHttpResponse']) is not None:
    setv(
        to_object, ['sdk_http_response'], getv(from_object, ['sdkHttpResponse'])
    )

  if getv(from_object, ['candidates']) is not None:
    setv(
        to_object,
        ['candidates'],
        [
            _Candidate_from_vertex(item, to_object)
            for item in getv(from_object, ['candidates'])
        ],
    )

  if getv(from_object, ['createTime']) is not None:
    setv(to_object, ['create_time'], getv(from_object, ['createTime']))

  if getv(from_object, ['responseId']) is not None:
    setv(to_object, ['response_id'], getv(from_object, ['responseId']))

  if getv(from_object, ['modelVersion']) is not None:
    setv(to_object, ['model_version'], getv(from_object, ['modelVersion']))

  if getv(from_object, ['promptFeedback']) is not None:
    setv(to_object, ['prompt_feedback'], getv(from_object, ['promptFeedback']))

  if getv(from_object, ['usageMetadata']) is not None:
    setv(to_object, ['usage_metadata'], getv(from_object, ['usageMetadata']))

  return to_object


def _InlinedResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}

  return to_object


def _BatchJobDestination_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['predictionsFormat']) is not None:
    setv(to_object, ['format'], getv(from_object, ['predictionsFormat']))

  if getv(from_object, ['gcsDestination', 'outputUriPrefix']) is not None:
    setv(
        to_object,
        ['gcs_uri'],
        getv(from_object, ['gcsDestination', 'outputUriPrefix']),
    )

  if getv(from_object, ['bigqueryDestination', 'outputUri']) is not None:
    setv(
        to_object,
        ['bigquery_uri'],
        getv(from_object, ['bigqueryDestination', 'outputUri']),
    )

  return to_object


def _BatchJob_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['displayName']) is not None:
    setv(to_object, ['display_name'], getv(from_object, ['displayName']))

  if getv(from_object, ['state']) is not None:
    setv(to_object, ['state'], t.t_job_state(getv(from_object, ['state'])))

  if getv(from_object, ['error']) is not None:
    setv(
        to_object,
        ['error'],
        _JobError_from_vertex(getv(from_object, ['error']), to_object),
    )

  if getv(from_object, ['createTime']) is not None:
    setv(to_object, ['create_time'], getv(from_object, ['createTime']))

  if getv(from_object, ['startTime']) is not None:
    setv(to_object, ['start_time'], getv(from_object, ['startTime']))

  if getv(from_object, ['endTime']) is not None:
    setv(to_object, ['end_time'], getv(from_object, ['endTime']))

  if getv(from_object, ['updateTime']) is not None:
    setv(to_object, ['update_time'], getv(from_object, ['updateTime']))

  if getv(from_object, ['model']) is not None:
    setv(to_object, ['model'], getv(from_object, ['model']))

  if getv(from_object, ['inputConfig']) is not None:
    setv(
        to_object,
        ['src'],
        _BatchJobSource_from_vertex(
            getv(from_object, ['inputConfig']), to_object
        ),
    )

  if getv(from_object, ['outputConfig']) is not None:
    setv(
        to_object,
        ['dest'],
        _BatchJobDestination_from_vertex(
            getv(from_object, ['outputConfig']), to_object
        ),
    )

  return to_object


def _ListBatchJobsResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['nextPageToken']) is not None:
    setv(to_object, ['next_page_token'], getv(from_object, ['nextPageToken']))

  if getv(from_object, ['batchPredictionJobs']) is not None:
    setv(
        to_object,
        ['batch_jobs'],
        [
            _BatchJob_from_vertex(item, to_object)
            for item in getv(from_object, ['batchPredictionJobs'])
        ],
    )

  return to_object


def _DeleteResourceJob_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
  to_object: dict[str, Any] = {}
  if getv(from_object, ['name']) is not None:
    setv(to_object, ['name'], getv(from_object, ['name']))

  if getv(from_object, ['done']) is not None:
    setv(to_object, ['done'], getv(from_object, ['done']))

  if getv(from_object, ['error']) is not None:
    setv(
        to_object,
        ['error'],
        _JobError_from_vertex(getv(from_object, ['error']), to_object),
    )

  return to_object


class Batches(_api_module.BaseModule):

  def _create(
      self,
      *,
      model: Optional[str] = None,
      src: Union[types.BatchJobSourceUnion, types.BatchJobSourceUnionDict],
      config: Optional[types.CreateBatchJobConfigOrDict] = None,
  ) -> types.BatchJob:
    parameter_model = types._CreateBatchJobParameters(
        model=model,
        src=src,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _CreateBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs'
    else:
      request_dict = _CreateBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = '{model}:batchGenerateContent'.format_map(request_url_dict)
      else:
        path = '{model}:batchGenerateContent'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = self._api_client.request(
        'post', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _BatchJob_from_vertex(response_dict)

    else:
      response_dict = _BatchJob_from_mldev(response_dict)

    return_value = types.BatchJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  def get(
      self, *, name: str, config: Optional[types.GetBatchJobConfigOrDict] = None
  ) -> types.BatchJob:
    """Gets a batch job.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the Vertex AI client. Or
          "batches/abc" using the Gemini Developer AI client.

    Returns:
      A BatchJob object that contains details about the batch job.

    Usage:

    .. code-block:: python

      batch_job = client.batches.get(name='123456789')
      print(f"Batch job: {batch_job.name}, state {batch_job.state}")
    """

    parameter_model = types._GetBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _GetBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}'
    else:
      request_dict = _GetBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}'.format_map(request_url_dict)
      else:
        path = 'batches/{name}'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = self._api_client.request('get', path, request_dict, http_options)

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _BatchJob_from_vertex(response_dict)

    else:
      response_dict = _BatchJob_from_mldev(response_dict)

    return_value = types.BatchJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  def cancel(
      self,
      *,
      name: str,
      config: Optional[types.CancelBatchJobConfigOrDict] = None,
  ) -> None:
    """Cancels a batch job.

    Only available for batch jobs that are running or pending.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the Vertex AI client. Or
          "batches/abc" using the Gemini Developer AI client.

    Usage:

    .. code-block:: python

      client.batches.cancel(name='123456789')
    """

    parameter_model = types._CancelBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _CancelBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}:cancel'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}:cancel'
    else:
      request_dict = _CancelBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}:cancel'.format_map(request_url_dict)
      else:
        path = 'batches/{name}:cancel'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = self._api_client.request(
        'post', path, request_dict, http_options
    )

  def _list(
      self, *, config: Optional[types.ListBatchJobsConfigOrDict] = None
  ) -> types.ListBatchJobsResponse:
    parameter_model = types._ListBatchJobsParameters(
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _ListBatchJobsParameters_to_vertex(parameter_model)
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs'
    else:
      request_dict = _ListBatchJobsParameters_to_mldev(parameter_model)
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches'.format_map(request_url_dict)
      else:
        path = 'batches'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = self._api_client.request('get', path, request_dict, http_options)

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _ListBatchJobsResponse_from_vertex(response_dict)

    else:
      response_dict = _ListBatchJobsResponse_from_mldev(response_dict)

    return_value = types.ListBatchJobsResponse._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  def delete(
      self,
      *,
      name: str,
      config: Optional[types.DeleteBatchJobConfigOrDict] = None,
  ) -> types.DeleteResourceJob:
    """Deletes a batch job.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the client.

    Returns:
      A DeleteResourceJob object that shows the status of the deletion.

    Usage:

    .. code-block:: python

      client.batches.delete(name='123456789')
    """

    parameter_model = types._DeleteBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _DeleteBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}'
    else:
      request_dict = _DeleteBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}'.format_map(request_url_dict)
      else:
        path = 'batches/{name}'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = self._api_client.request(
        'delete', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _DeleteResourceJob_from_vertex(response_dict)

    else:
      response_dict = _DeleteResourceJob_from_mldev(response_dict)

    return_value = types.DeleteResourceJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  def create(
      self,
      *,
      model: str,
      src: Union[types.BatchJobSourceUnion, types.BatchJobSourceUnionDict],
      config: Optional[types.CreateBatchJobConfigOrDict] = None,
  ) -> types.BatchJob:
    """Creates a batch job.

    Args:
      model (str): The model to use for the batch job.
      src: The source of the batch job. Currently Vertex AI supports GCS URI(-s)
        or BigQuery URI. Example: "gs://path/to/input/data" or
        "bq://projectId.bqDatasetId.bqTableId". Gemini Developer API supports
        List of inlined_request, or file name. Example: "files/file_name".
      config (CreateBatchJobConfig): Optional configuration for the batch job.

    Returns:
      A BatchJob object that contains details about the batch job.

    Usage:

    .. code-block:: python

      batch_job = client.batches.create(
          model="gemini-2.0-flash-001",
          src="gs://path/to/input/data",
      )
      print(batch_job.state)
    """
    if self._api_client.vertexai:
      if isinstance(src, list):
        raise ValueError(
            'inlined_requests is not supported in Vertex AI. Please use'
            ' Google Cloud Storage URI or BigQuery URI instead.'
        )

      config = _extra_utils.format_destination(src, config)
    return self._create(model=model, src=src, config=config)

  def list(
      self, *, config: Optional[types.ListBatchJobsConfigOrDict] = None
  ) -> Pager[types.BatchJob]:
    """Lists batch jobs.

    Args:
      config (ListBatchJobsConfig): Optional configuration for the list request.

    Returns:
      A Pager object that contains one page of batch jobs. When iterating over
      the pager, it automatically fetches the next page if there are more.

    Usage:

    .. code-block:: python

      batch_jobs = client.batches.list(config={"page_size": 10})
      for batch_job in batch_jobs:
        print(f"Batch job: {batch_job.name}, state {batch_job.state}")
    """
    if config is None:
      config = types.ListBatchJobsConfig()
    return Pager(
        'batch_jobs',
        self._list,
        self._list(config=config),
        config,
    )


class AsyncBatches(_api_module.BaseModule):

  async def _create(
      self,
      *,
      model: Optional[str] = None,
      src: Union[types.BatchJobSourceUnion, types.BatchJobSourceUnionDict],
      config: Optional[types.CreateBatchJobConfigOrDict] = None,
  ) -> types.BatchJob:
    parameter_model = types._CreateBatchJobParameters(
        model=model,
        src=src,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _CreateBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs'
    else:
      request_dict = _CreateBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = '{model}:batchGenerateContent'.format_map(request_url_dict)
      else:
        path = '{model}:batchGenerateContent'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = await self._api_client.async_request(
        'post', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _BatchJob_from_vertex(response_dict)

    else:
      response_dict = _BatchJob_from_mldev(response_dict)

    return_value = types.BatchJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  async def get(
      self, *, name: str, config: Optional[types.GetBatchJobConfigOrDict] = None
  ) -> types.BatchJob:
    """Gets a batch job.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the Vertex AI client. Or
          "batches/abc" using the Gemini Developer AI client.

    Returns:
      A BatchJob object that contains details about the batch job.

    Usage:

    .. code-block:: python

      batch_job = await client.aio.batches.get(name='123456789')
      print(f"Batch job: {batch_job.name}, state {batch_job.state}")
    """

    parameter_model = types._GetBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _GetBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}'
    else:
      request_dict = _GetBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}'.format_map(request_url_dict)
      else:
        path = 'batches/{name}'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = await self._api_client.async_request(
        'get', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _BatchJob_from_vertex(response_dict)

    else:
      response_dict = _BatchJob_from_mldev(response_dict)

    return_value = types.BatchJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  async def cancel(
      self,
      *,
      name: str,
      config: Optional[types.CancelBatchJobConfigOrDict] = None,
  ) -> None:
    """Cancels a batch job.

    Only available for batch jobs that are running or pending.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the Vertex AI client. Or
          "batches/abc" using the Gemini Developer AI client.

    Usage:

    .. code-block:: python

      await client.aio.batches.cancel(name='123456789')
    """

    parameter_model = types._CancelBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _CancelBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}:cancel'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}:cancel'
    else:
      request_dict = _CancelBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}:cancel'.format_map(request_url_dict)
      else:
        path = 'batches/{name}:cancel'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = await self._api_client.async_request(
        'post', path, request_dict, http_options
    )

  async def _list(
      self, *, config: Optional[types.ListBatchJobsConfigOrDict] = None
  ) -> types.ListBatchJobsResponse:
    parameter_model = types._ListBatchJobsParameters(
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _ListBatchJobsParameters_to_vertex(parameter_model)
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs'
    else:
      request_dict = _ListBatchJobsParameters_to_mldev(parameter_model)
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches'.format_map(request_url_dict)
      else:
        path = 'batches'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = await self._api_client.async_request(
        'get', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _ListBatchJobsResponse_from_vertex(response_dict)

    else:
      response_dict = _ListBatchJobsResponse_from_mldev(response_dict)

    return_value = types.ListBatchJobsResponse._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  async def delete(
      self,
      *,
      name: str,
      config: Optional[types.DeleteBatchJobConfigOrDict] = None,
  ) -> types.DeleteResourceJob:
    """Deletes a batch job.

    Args:
      name (str): A fully-qualified BatchJob resource name or ID.
        Example: "projects/.../locations/.../batchPredictionJobs/456" or "456"
          when project and location are initialized in the client.

    Returns:
      A DeleteResourceJob object that shows the status of the deletion.

    Usage:

    .. code-block:: python

      await client.aio.batches.delete(name='123456789')
    """

    parameter_model = types._DeleteBatchJobParameters(
        name=name,
        config=config,
    )

    request_url_dict: Optional[dict[str, str]]

    if self._api_client.vertexai:
      request_dict = _DeleteBatchJobParameters_to_vertex(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batchPredictionJobs/{name}'.format_map(request_url_dict)
      else:
        path = 'batchPredictionJobs/{name}'
    else:
      request_dict = _DeleteBatchJobParameters_to_mldev(
          self._api_client, parameter_model
      )
      request_url_dict = request_dict.get('_url')
      if request_url_dict:
        path = 'batches/{name}'.format_map(request_url_dict)
      else:
        path = 'batches/{name}'
    query_params = request_dict.get('_query')
    if query_params:
      path = f'{path}?{urlencode(query_params)}'
    # TODO: remove the hack that pops config.
    request_dict.pop('config', None)

    http_options: Optional[types.HttpOptions] = None
    if (
        parameter_model.config is not None
        and parameter_model.config.http_options is not None
    ):
      http_options = parameter_model.config.http_options

    request_dict = _common.convert_to_dict(request_dict)
    request_dict = _common.encode_unserializable_types(request_dict)

    response = await self._api_client.async_request(
        'delete', path, request_dict, http_options
    )

    response_dict = '' if not response.body else json.loads(response.body)

    if self._api_client.vertexai:
      response_dict = _DeleteResourceJob_from_vertex(response_dict)

    else:
      response_dict = _DeleteResourceJob_from_mldev(response_dict)

    return_value = types.DeleteResourceJob._from_response(
        response=response_dict, kwargs=parameter_model.model_dump()
    )

    self._api_client._verify_response(return_value)
    return return_value

  async def create(
      self,
      *,
      model: str,
      src: Union[types.BatchJobSourceUnion, types.BatchJobSourceUnionDict],
      config: Optional[types.CreateBatchJobConfigOrDict] = None,
  ) -> types.BatchJob:
    """Creates a batch job asynchronously.

    Args:
      model (str): The model to use for the batch job.
      src: The source of the batch job. Currently Vertex AI supports GCS URI(-s)
        or BigQuery URI. Example: "gs://path/to/input/data" or
        "bq://projectId.bqDatasetId.bqTableId". Gemini Develop API supports List
        of inlined_request, or file name. Example: "files/file_name".
      config (CreateBatchJobConfig): Optional configuration for the batch job.

    Returns:
      A BatchJob object that contains details about the batch job.

    Usage:

    .. code-block:: python

      batch_job = await client.aio.batches.create(
          model="gemini-2.0-flash-001",
          src="gs://path/to/input/data",
      )
    """
    if self._api_client.vertexai:
      if isinstance(src, list):
        raise ValueError(
            'inlined_requests is not supported in Vertex AI. Please use'
            ' Google Cloud Storage URI or BigQuery URI instead.'
        )

      config = _extra_utils.format_destination(src, config)
    return await self._create(model=model, src=src, config=config)

  async def list(
      self, *, config: Optional[types.ListBatchJobsConfigOrDict] = None
  ) -> AsyncPager[types.BatchJob]:
    """Lists batch jobs asynchronously.

    Args:
      config (ListBatchJobsConfig): Optional configuration for the list request.

    Returns:
      A Pager object that contains one page of batch jobs. When iterating over
      the pager, it automatically fetches the next page if there are more.

    Usage:

    .. code-block:: python

      batch_jobs = await client.aio.batches.list(config={'page_size': 5})
      print(f"current page: {batch_jobs.page}")
      await batch_jobs_pager.next_page()
      print(f"next page: {batch_jobs_pager.page}")
    """
    if config is None:
      config = types.ListBatchJobsConfig()
    return AsyncPager(
        'batch_jobs',
        self._list,
        await self._list(config=config),
        config,
    )
