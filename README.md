# 🌞 Solar Potential Analyzer

A comprehensive solar potential assessment tool powered by Google APIs and Gemini AI, featuring **enhanced solar panel mapping** with GeoTiff-style visualization similar to Google's Project Sunroof.

## ✨ Key Features

### 🎯 Enhanced Solar Panel Mapping
- **Energy-based color visualization**: Panels colored by yearly energy production (green = high, red = low)
- **Accurate geographic positioning**: Real Google Solar API coordinates converted to pixel positions
- **Realistic panel orientation**: Proper rotation based on roof segment azimuth and orientation
- **GeoTiff-style heatmap integration**: Solar flux data overlaid on satellite imagery

### 🛰️ Comprehensive Analysis
- High-resolution satellite imagery analysis
- Google Solar API integration for precise solar data
- AI-powered roof structure analysis using Gemini
- Interactive visualization with multiple data layers
- Detailed financial projections and ROI calculations

### 📊 Professional Reporting
- Project Sunroof-quality visualizations
- Comprehensive PDF reports
- Interactive charts and metrics
- Customizable analysis parameters

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Google Cloud Platform account with API access
- Google AI Studio account for Gemini API

### 1. Clone and Setup
```bash
git clone <repository-url>
cd SolarPotentialAnalyzer
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure API Keys

#### Option A: Using .env file (Recommended)
```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your API keys
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
```

#### Option B: Environment Variables
```bash
export GOOGLE_API_KEY="your_google_api_key_here"
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 4. Run the Application

#### Option A: Using the setup script (Recommended)
```bash
python run_local.py
```

#### Option B: Direct Streamlit
```bash
streamlit run app.py
```

The application will open in your browser at `http://localhost:8501`

## 🔑 API Setup Instructions

### Google Cloud Platform APIs

1. **Create a Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Required APIs**
   - [Maps Static API](https://console.cloud.google.com/apis/library/static-maps-backend.googleapis.com)
   - [Geocoding API](https://console.cloud.google.com/apis/library/geocoding-backend.googleapis.com)
   - [Solar API](https://console.cloud.google.com/apis/library/solar.googleapis.com)
   - [Vision API](https://console.cloud.google.com/apis/library/vision.googleapis.com) (optional)

3. **Create API Key**
   - Go to [Credentials](https://console.cloud.google.com/apis/credentials)
   - Click "Create Credentials" → "API Key"
   - Copy the generated key

### Gemini AI API

1. **Get Gemini API Key**
   - Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Click "Create API Key"
   - Copy the generated key

## 🎨 Enhanced Solar Panel Mapping

This implementation combines the best of both worlds:

### Energy-Based Visualization
- **Color Palette**: Green (high energy) → Yellow → Red (low energy)
- **Real Data**: Uses actual Google Solar API panel energy values
- **Accurate Positioning**: Converts lat/lng coordinates to pixel positions

### GeoTiff-Style Processing
- **Heatmap Overlays**: Solar flux data blended with satellite imagery
- **Multi-layer Composition**: Satellite + panels + heatmaps combined
- **Professional Quality**: Project Sunroof-level visualization

### Technical Implementation
```python
# Enhanced panel mapping with energy colors
composite_path = image_processor.create_composite_solar_visualization(
    satellite_image_path, 
    analysis_data
)

# Individual components
panel_overlay = image_processor.create_solar_overlay(satellite_path, analysis_data)
heatmap_overlay = image_processor.render_solar_heatmap_overlay(satellite_path, solar_datalayers)
```

## 📁 Project Structure

```
SolarPotentialAnalyzer/
├── app.py                          # Main Streamlit application
├── requirements.txt                # Python dependencies
├── run_local.py                   # Local setup and run script
├── .env.example                   # Environment variables template
├── ENHANCED_SOLAR_MAPPING.md      # Detailed technical documentation
├── components/                    # UI components
│   ├── address_input.py          # Address input interface
│   ├── solar_visualization.py    # Enhanced solar visualization
│   ├── solar_heatmap_display.py  # Heatmap display components
│   └── results_display.py        # Results and metrics display
├── utils/                         # Core utilities
│   ├── google_apis.py            # Google API integrations
│   ├── gemini_analyzer.py        # AI-powered analysis
│   ├── image_processor.py        # Enhanced image processing
│   └── pdf_generator.py          # Report generation
└── test_*.py                      # Test suites
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
# Test enhanced image processing
python test_image_processor_only.py

# Test full functionality (requires API keys)
python test_enhanced_solar_mapping.py
```

## 🔧 Configuration Options

### Analysis Parameters
- **Panel Efficiency**: 15-22% (default: 20%)
- **System Losses**: 10-20% (default: 14%)
- **Electricity Rate**: $0.08-0.30/kWh (default: $0.12)
- **Zoom Level**: 18-21 (default: 20)

### Visualization Options
- Enhanced solar panel mapping (default: enabled)
- GeoTiff-style heatmap overlays
- Multiple data layer combinations
- Interactive controls and toggles

## 🚨 Troubleshooting

### Common Issues

1. **API Key Errors**
   ```
   ValueError: Google API key not found in environment variables
   ```
   **Solution**: Ensure API keys are properly set in `.env` file or environment variables

2. **Import Errors**
   ```
   ImportError: cannot import name 'genai' from 'google'
   ```
   **Solution**: Install correct package: `pip install google-genai`

3. **NumPy Compatibility Issues**
   ```
   AttributeError: _ARRAY_API not found
   ```
   **Solution**: Update NumPy: `pip install numpy>=1.24.0`

### Performance Tips
- Use zoom level 20 for best balance of detail and speed
- Enable caching for repeated analyses of the same location
- Consider using `opencv-python-headless` for server deployments

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section above
- Review the detailed documentation in `ENHANCED_SOLAR_MAPPING.md`
- Run the test suite to verify functionality

---

**🌟 Enhanced with Project Sunroof-quality solar panel mapping and GeoTiff-style visualization!**
